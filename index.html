<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار البكالوريا</title>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@400;700&family=Amiri:wght@400;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Amiri', 'Cairo', Arial, sans-serif;
            background-color: #e8d9b5; /* Parchment color */
            text-align: center;
            padding: 20px;
            background-image: url('https://www.transparenttextures.com/patterns/old-paper.png');
            color: #5d3a1a; /* Dark brown text */
        }

        /* Scroll decorations */
        .scroll-top {
            width: 80%;
            height: 40px;
            margin: 0 auto -20px auto;
            background-image: url('https://www.transparenttextures.com/patterns/old-paper.png');
            background-color: #d9c7a0;
            border-top-left-radius: 20px;
            border-top-right-radius: 20px;
            border: 2px solid #8b4513;
            border-bottom: none;
            position: relative;
            z-index: 1;
        }

        .scroll-bottom {
            width: 80%;
            height: 40px;
            margin: -20px auto 40px auto;
            background-image: url('https://www.transparenttextures.com/patterns/old-paper.png');
            background-color: #d9c7a0;
            border-bottom-left-radius: 20px;
            border-bottom-right-radius: 20px;
            border: 2px solid #8b4513;
            border-top: none;
            position: relative;
            z-index: 1;
        }

        .scroll-decoration {
            position: absolute;
            width: 30px;
            height: 100%;
            top: 0;
            background-image: url('https://www.transparenttextures.com/patterns/old-paper.png');
            background-color: #d9c7a0;
            border: 2px solid #8b4513;
            z-index: 0;
        }

        .scroll-decoration.left {
            left: -15px;
            border-right: none;
            border-top-left-radius: 15px;
            border-bottom-left-radius: 15px;
        }

        .scroll-decoration.right {
            right: -15px;
            border-left: none;
            border-top-right-radius: 15px;
            border-bottom-right-radius: 15px;
        }

        .section {
            display: none;
        }

        .section.active {
            display: block;
        }

        .menu-container {
            width: 80%;
            max-width: 600px;
            margin: auto;
            padding: 30px;
            background-color: #f5e7c1; /* Light parchment color */
            border-radius: 10px;
            box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
            border: 2px solid #8b4513; /* Brown border */
            background-image: url('https://www.transparenttextures.com/patterns/parchment.png');
            position: relative;
        }

        .menu-container:before, .menu-container:after {
            content: '';
            position: absolute;
            width: 100%;
            height: 20px;
            background-image: url('https://www.transparenttextures.com/patterns/old-paper.png');
            left: 0;
        }

        .menu-container:before {
            top: -10px;
            border-top-left-radius: 10px;
            border-top-right-radius: 10px;
        }

        .menu-container:after {
            bottom: -10px;
            border-bottom-left-radius: 10px;
            border-bottom-right-radius: 10px;
        }

        .quiz-container {
            width: 80%;
            margin: auto;
            padding: 30px;
            background-color: #f5e7c1; /* Light parchment color */
            border: 2px solid #8b4513; /* Brown border */
            border-radius: 10px;
            box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
            background-image: url('https://www.transparenttextures.com/patterns/parchment.png');
            position: relative;
        }

        .quiz-container:before, .quiz-container:after {
            content: '';
            position: absolute;
            width: 100%;
            height: 20px;
            background-image: url('https://www.transparenttextures.com/patterns/old-paper.png');
            left: 0;
        }

        .quiz-container:before {
            top: -10px;
            border-top-left-radius: 10px;
            border-top-right-radius: 10px;
        }

        .quiz-container:after {
            bottom: -10px;
            border-bottom-left-radius: 10px;
            border-bottom-right-radius: 10px;
        }

        .main-title {
            margin-bottom: 30px;
        }

        .site-title {
            color: #5d3a1a; /* Dark brown */
            margin: 10px 0 30px 0;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.2);
            font-size: 2.5em;
            font-family: 'Amiri', serif;
            font-weight: bold;
            letter-spacing: 1px;
        }

        .scroll-icon {
            font-size: 3em;
            margin-bottom: 10px;
            animation: float 3s ease-in-out infinite;
        }

        @keyframes float {
            0% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
            100% { transform: translateY(0px); }
        }

        h1 {
            color: #5d3a1a; /* Dark brown */
            margin-bottom: 30px;
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.2);
            font-size: 2.2em;
            position: relative;
            display: inline-block;
        }

        h1:after {
            content: '';
            position: absolute;
            width: 80%;
            height: 2px;
            background-color: #8b4513;
            bottom: -10px;
            left: 10%;
        }

        h2 {
            color: #5d3a1a; /* Dark brown */
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.2);
            font-size: 1.8em;
        }

        .category-container {
            background-color: rgba(245, 231, 193, 0.5);
            border-radius: 8px;
            padding: 15px;
            margin: 20px 0;
            border: 1px dashed #8b4513;
        }

        .category-title {
            color: #5d3a1a;
            margin-top: 0;
            margin-bottom: 15px;
            font-size: 1.5em;
            border-bottom: 1px solid #8b4513;
            padding-bottom: 5px;
            display: inline-block;
        }

        .subcategory-container {
            background-color: rgba(245, 231, 193, 0.5);
            border-radius: 8px;
            padding: 10px;
            margin: 10px 0;
            border: 1px dashed #8b4513;
        }

        .subcategory-title {
            color: #5d3a1a;
            margin-top: 0;
            margin-bottom: 10px;
            font-size: 1.2em;
            border-bottom: 1px dotted #8b4513;
            padding-bottom: 3px;
            display: inline-block;
        }

        .submenu-button {
            width: 70%;
            margin: 8px auto;
            padding: 10px 15px;
            font-size: 0.9em;
        }

        .menu-button {
            display: block;
            width: 80%;
            margin: 15px auto;
            padding: 15px 20px;
            font-size: 20px;
            font-family: 'Cairo', Arial, sans-serif;
            color: #f5e7c1; /* Light parchment color */
            background-color: #8b4513; /* Brown */
            border: 2px solid #5d3a1a; /* Darker brown */
            border-radius: 5px;
            cursor: pointer;
            transition: all 0.3s;
            position: relative;
            overflow: hidden;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
        }

        .menu-button:hover {
            background-color: #5d3a1a; /* Darker brown */
            transform: translateY(-2px);
            box-shadow: 0 6px 12px rgba(0, 0, 0, 0.3);
        }

        .menu-button:active {
            transform: translateY(1px);
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        }

        .back-button {
            position: fixed;
            top: 20px;
            left: 20px;
            padding: 10px 20px;
            background-color: #8b4513; /* Brown */
            color: #f5e7c1; /* Light parchment color */
            border: 2px solid #5d3a1a; /* Darker brown */
            border-radius: 5px;
            cursor: pointer;
            font-family: 'Cairo', Arial, sans-serif;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
            transition: all 0.3s;
        }

        .back-button:hover {
            background-color: #5d3a1a; /* Darker brown */
            transform: translateY(-2px);
        }

        .back-button:active {
            transform: translateY(1px);
        }

        #answers {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .answer-input-container {
            margin: 20px 0;
        }

        #answer-input {
            width: 100%;
            padding: 15px;
            border: 2px solid #8b4513; /* Brown border */
            border-radius: 5px;
            font-family: 'Cairo', Arial, sans-serif;
            font-size: 16px;
            resize: vertical;
            direction: rtl;
            background-color: rgba(245, 231, 193, 0.7); /* Transparent light parchment */
            box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .date-input {
            width: 60px;
            padding: 8px;
            margin: 0 5px;
            text-align: center;
            border: 2px solid #8b4513; /* Brown border */
            border-radius: 5px;
            font-family: 'Cairo', Arial, sans-serif;
            background-color: rgba(245, 231, 193, 0.7); /* Transparent light parchment */
            box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .date-option {
            margin: 10px 0;
            padding: 10px;
            background-color: rgba(245, 231, 193, 0.5);
            border-radius: 5px;
            border: 1px solid #8b4513;
            text-align: right;
            transition: all 0.3s;
        }

        .date-option:hover {
            background-color: rgba(245, 231, 193, 0.8);
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .date-label {
            margin-right: 10px;
            font-size: 16px;
            cursor: pointer;
            font-weight: bold;
        }

        .correct-date {
            color: #2e7d32; /* Green */
            position: relative;
        }

        .correct-date::after {
            content: ' ✓';
            color: #2e7d32;
        }

        .incorrect-date {
            color: #c62828; /* Red */
            position: relative;
        }

        .incorrect-date::after {
            content: ' ✗';
            color: #c62828;
        }

        .progress-indicator {
            font-size: 14px;
            color: #5d3a1a; /* Dark brown */
            padding: 5px 10px;
            background-color: rgba(245, 231, 193, 0.5);
            border-radius: 15px;
            border: 1px solid #8b4513; /* Brown border */
            position: absolute;
            top: 10px;
            right: 10px;
            z-index: 10;
        }

        #geo-submit, #geo-continue, #geo-reset, #geo-skip, #geo-retry, #geo-hint,
        #hist-submit, #hist-continue, #hist-reset, #hist-skip, #hist-retry {
            margin: 10px;
            padding: 12px 25px;
            border: 2px solid #5d3a1a; /* Darker brown */
            border-radius: 5px;
            cursor: pointer;
            font-family: 'Cairo', Arial, sans-serif;
            color: #f5e7c1; /* Light parchment color */
            font-weight: bold;
            transition: all 0.3s;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
        }

        #geo-submit, #hist-submit, #crossword-check {
            background-color: #8b4513; /* Brown */
        }

        #geo-submit:hover, #hist-submit:hover, #crossword-check:hover {
            background-color: #5d3a1a; /* Darker brown */
            transform: translateY(-2px);
        }

        #geo-skip, #hist-skip, #crossword-skip {
            background-color: #20B2AA; /* Light Sea Green (Turquoise) */
            color: #f5e7c1; /* Light parchment color */
        }

        #geo-skip:hover, #hist-skip:hover, #crossword-skip:hover {
            background-color: #008B8B; /* Dark Cyan (Darker Turquoise) */
            transform: translateY(-2px);
        }

        #geo-hint {
            background-color: #9370DB; /* Medium Purple */
            color: #f5e7c1; /* Light parchment color */
        }

        #geo-hint:hover {
            background-color: #7B68EE; /* Medium Slate Blue (Darker Purple) */
            transform: translateY(-2px);
        }

        #geo-continue, #hist-continue, #crossword-continue {
            background-color: #6b8e23; /* Olive green - more vintage feel */
        }

        #geo-continue:hover, #hist-continue:hover, #crossword-continue:hover {
            background-color: #556b2f; /* Darker olive green */
            transform: translateY(-2px);
        }

        #geo-retry, #hist-retry {
            background-color: #4682B4; /* Steel blue */
            color: #f5e7c1; /* Light parchment color */
        }

        #geo-retry:hover, #hist-retry:hover {
            background-color: #36648B; /* Darker blue */
            transform: translateY(-2px);
        }

        #crossword-clear {
            background-color: #FF8C00; /* Dark Orange */
            color: #f5e7c1; /* Light parchment color */
        }

        #crossword-clear:hover {
            background-color: #E67300; /* Darker Orange */
            transform: translateY(-2px);
        }

        #geo-reset, #hist-reset, #crossword-reset {
            background-color: #8b0000; /* Dark red - more vintage feel */
        }

        #geo-reset:hover, #hist-reset:hover, #crossword-reset:hover {
            background-color: #660000; /* Darker red */
            transform: translateY(-2px);
        }

        #geo-submit:active, #hist-submit:active, #crossword-check:active,
        #geo-skip:active, #hist-skip:active, #crossword-skip:active,
        #geo-hint:active,
        #geo-continue:active, #hist-continue:active, #crossword-continue:active,
        #geo-retry:active, #hist-retry:active, #crossword-clear:active,
        #geo-reset:active, #hist-reset:active, #crossword-reset:active {
            transform: translateY(1px);
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        }

        #geo-result, #hist-result {
            font-size: 18px;
            font-weight: bold;
            margin-top: 20px;
            text-align: center;
            line-height: 1.6;
            padding: 15px;
            border-radius: 5px;
            background-color: rgba(245, 231, 193, 0.7);
            border: 1px solid #8b4513;
        }

        .correct-answer {
            margin-top: 10px;
            padding: 10px;
            background-color: rgba(255, 255, 255, 0.5);
            border-radius: 5px;
            font-size: 16px;
            text-align: right;
            direction: rtl;
            border: 1px dashed #8b4513;
        }

        .comparison-container {
            margin-top: 15px;
            padding: 10px;
            background-color: rgba(255, 255, 255, 0.7);
            border-radius: 5px;
            text-align: right;
            direction: rtl;
            border: 1px solid #8b4513;
            line-height: 1.8;
        }

        .similarity-score {
            background-color: rgba(245, 231, 193, 0.7);
            padding: 8px;
            margin: 10px 0;
            border-radius: 5px;
            font-weight: bold;
            border: 1px dashed #8b4513;
        }

        .score-details {
            font-size: 0.9em;
            opacity: 0.8;
            margin-right: 10px;
            font-weight: normal;
        }

        .comparison-title {
            font-weight: bold;
            margin-bottom: 8px;
            color: #5d3a1a;
        }

        .user-answer-comparison {
            padding: 8px;
            background-color: rgba(245, 245, 245, 0.5);
            border-radius: 5px;
        }

        .correct-word {
            color: #2e7d32; /* Green */
            font-weight: bold;
        }

        .partial-word {
            color: #ff8f00; /* Dark yellow/amber */
            font-weight: bold;
        }

        .incorrect-word {
            color: #c62828; /* Red */
            text-decoration: line-through;
            font-weight: normal;
        }

        .missing-word {
            color: #c62828; /* Red */
            font-style: italic;
            background-color: rgba(198, 40, 40, 0.1);
            padding: 0 3px;
            border-radius: 3px;
        }

        .hint-container {
            background-color: rgba(147, 112, 219, 0.2); /* Light purple background */
            padding: 15px;
            border-radius: 5px;
            border: 1px dashed #9370DB; /* Purple border */
            margin: 10px 0;
            text-align: right;
            direction: rtl;
        }

        .hint-penalty {
            color: #9370DB; /* Purple */
            font-size: 14px;
            margin-top: 8px;
            font-style: italic;
        }

        .multi-answer-container {
            display: flex;
            flex-direction: row;
            justify-content: space-between;
            gap: 20px;
            margin-bottom: 15px;
        }

        .answer-section {
            flex: 1;
            display: flex;
            flex-direction: column;
            background-color: rgba(245, 231, 193, 0.3);
            padding: 10px;
            border-radius: 5px;
            border: 1px dashed #8b4513;
        }

        .answer-label {
            font-weight: bold;
            margin-bottom: 5px;
            color: #5d3a1a;
            text-align: center;
        }

        .bio-answer-input {
            width: 100%;
            min-height: 60px;
            padding: 8px;
            margin-bottom: 10px;
            border: 1px solid #8b4513;
            border-radius: 4px;
            background-color: rgba(255, 255, 255, 0.7);
            font-family: 'Cairo', Arial, sans-serif;
            resize: vertical;
            direction: rtl;
        }

        .answer-result {
            min-height: 30px;
            font-size: 14px;
        }

        /* Physics Styles */
        .physics-content {
            display: flex;
            flex-direction: row;
            flex-wrap: wrap;
            gap: 20px;
            justify-content: space-between;
            align-items: flex-start;
            margin: 20px 0;
            direction: rtl;
        }

        .physics-image-container {
            flex: 0 0 300px;
            margin-bottom: 20px;
        }

        .physics-text {
            flex: 1 1 500px;
            text-align: right;
            line-height: 1.6;
        }

        .physics-text h3 {
            color: #5d3a1a;
            border-bottom: 1px solid #8b4513;
            padding-bottom: 5px;
            margin-top: 20px;
            margin-bottom: 10px;
        }

        .physics-text ul, .physics-text ol {
            margin: 10px 20px;
            padding-right: 20px;
        }

        .physics-text li {
            margin-bottom: 8px;
        }

        .physics-note {
            background-color: rgba(245, 231, 193, 0.7);
            border: 1px dashed #8b4513;
            border-radius: 5px;
            padding: 10px 15px;
            margin: 20px 0;
        }

        .physics-formula {
            font-family: 'Amiri', serif;
            font-size: 1.2em;
            text-align: center;
            margin: 15px 0;
            direction: ltr;
        }

        .physics-example {
            background-color: rgba(245, 231, 193, 0.5);
            border: 1px solid #8b4513;
            border-radius: 8px;
            padding: 10px 15px;
            margin: 15px 0;
        }

        /* Redox Exercise Styles */
        .redox-exercise {
            background-color: rgba(245, 231, 193, 0.7);
            border: 1px solid #8b4513;
            border-radius: 8px;
            padding: 15px;
            margin: 20px 0;
            box-shadow: 0 2px 5px rgba(139, 69, 19, 0.2);
        }

        .redox-exercise h4 {
            color: #5d3a1a;
            margin-top: 0;
            margin-bottom: 10px;
            border-bottom: 1px solid #8b4513;
            padding-bottom: 5px;
        }

        .redox-workspace {
            margin: 15px 0;
        }

        .redox-target-container {
            margin-bottom: 15px;
        }

        .redox-target-label {
            font-weight: bold;
            margin-bottom: 5px;
            color: #5d3a1a;
        }

        .redox-target {
            min-height: 60px;
            border: 2px dashed #8b4513;
            border-radius: 5px;
            padding: 10px;
            margin-bottom: 15px;
            background-color: rgba(255, 255, 255, 0.5);
            display: flex;
            flex-wrap: wrap;
            gap: 5px;
            align-items: center;
        }

        /* LTR direction for chemical equations */
        [dir="ltr"].redox-target {
            direction: ltr;
            text-align: left;
            justify-content: flex-start;
        }

        /* Redox pair styling */
        .redox-pair-container {
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 15px;
        }

        .redox-ox, .redox-red {
            flex: 1;
            min-width: 120px;
            margin-bottom: 0;
        }

        .redox-separator {
            font-size: 24px;
            font-weight: bold;
            margin: 0 10px;
            color: #8b4513;
        }

        .redox-target p {
            width: 100%;
            text-align: center;
            color: #8b4513;
            margin: 0;
        }

        .redox-pieces {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-bottom: 15px;
            justify-content: center;
        }

        .redox-piece {
            padding: 8px 12px;
            background-color: #f5e7c1;
            border: 1px solid #8b4513;
            border-radius: 5px;
            cursor: grab;
            user-select: none;
            font-family: 'Amiri', serif;
            font-size: 1.1em;
            transition: all 0.2s;
        }

        .redox-piece:hover {
            background-color: #f0d89e;
            transform: translateY(-2px);
            box-shadow: 0 2px 3px rgba(139, 69, 19, 0.2);
        }

        .redox-piece.dragging {
            opacity: 0.6;
            cursor: grabbing;
        }

        .redox-piece.placed {
            margin: 0 2px;
        }

        .redox-feedback {
            min-height: 30px;
            margin-bottom: 15px;
            font-weight: bold;
            text-align: center;
        }

        .redox-feedback.correct {
            color: #4CAF50;
        }

        .redox-feedback.incorrect {
            color: #F44336;
        }

        .redox-buttons {
            display: flex;
            justify-content: center;
            gap: 10px;
        }

        .redox-buttons button {
            padding: 8px 15px;
            background-color: #8b4513;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            transition: background-color 0.2s;
        }

        .redox-buttons button:hover {
            background-color: #a0522d;
        }

        /* Learn button and lesson styles */
        #bio-learn {
            background-color: #20B2AA; /* Light sea green */
            color: white;
        }

        #bio-learn:hover {
            background-color: #48D1CC; /* Medium turquoise */
        }

        .lesson-container {
            background-color: rgba(245, 231, 193, 0.8);
            border: 1px solid #8b4513;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
            box-shadow: 0 2px 5px rgba(139, 69, 19, 0.2);
        }

        .lesson-title {
            color: #5d3a1a;
            font-size: 1.2em;
            margin-bottom: 10px;
            border-bottom: 1px solid #8b4513;
            padding-bottom: 5px;
        }

        .lesson-content {
            line-height: 1.6;
            text-align: right;
            direction: rtl;
        }

        .lesson-content p {
            margin-bottom: 10px;
        }

        .lesson-content strong {
            color: #5d3a1a;
        }

        /* Crossword Styles */
        .crossword-container {
            margin: 10px auto;
            direction: rtl;
            max-width: 95%;
        }

        .crossword-grid-container {
            display: flex;
            justify-content: center;
            align-items: flex-start;
            gap: 15px;
            margin: 10px auto;
        }

        .crossword-grid {
            display: grid;
            grid-gap: 2px;
            margin: 0;
            width: fit-content;
        }

        .question-images {
            display: flex;
            flex-direction: column;
            gap: 5px;
            max-width: 150px;
        }

        .question-image {
            width: 100%;
            max-width: 120px;
            height: auto;
            border: 1px solid #8b4513;
            border-radius: 5px;
            overflow: hidden;
            margin-bottom: 5px;
        }

        .question-image img {
            width: 100%;
            height: auto;
            object-fit: cover;
            display: block;
        }

        .question-image-caption {
            font-size: 10px;
            text-align: center;
            color: #5d3a1a;
            margin-top: 2px;
        }

        .crossword-cell {
            width: 28px;
            height: 28px;
            border: 1px solid #8b4513;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
            font-weight: bold;
            background-color: rgba(245, 231, 193, 0.7);
            position: relative;
        }

        .crossword-cell.empty {
            background-color: #5d3a1a;
            border: 1px solid #5d3a1a;
        }

        .crossword-cell.filled {
            background-color: #e6f7ff;
        }

        .crossword-cell.correct {
            background-color: rgba(46, 125, 50, 0.2); /* Light green */
        }

        .crossword-cell.incorrect {
            background-color: rgba(198, 40, 40, 0.2); /* Light red */
        }

        .crossword-cell.prefilled {
            background-color: rgba(255, 193, 7, 0.2); /* Light amber */
            font-weight: bold;
            color: #8b4513; /* Brown */
        }

        .crossword-cell-number {
            position: absolute;
            top: 1px;
            right: 1px;
            font-size: 8px;
            color: #5d3a1a;
        }

        .crossword-cell-symbol {
            position: absolute;
            bottom: 1px;
            left: 1px;
            font-size: 8px;
        }

        .crossword-cell.highlighted {
            background-color: rgba(255, 255, 0, 0.2); /* Light yellow */
        }

        .crossword-cell.droppable {
            background-color: rgba(105, 240, 174, 0.2); /* Light teal */
            box-shadow: inset 0 0 5px #4CAF50;
        }

        .crossword-cell.drag-over {
            background-color: rgba(105, 240, 174, 0.4); /* Brighter teal */
            box-shadow: inset 0 0 8px #4CAF50;
            transform: scale(1.05);
        }

        .question-clues {
            margin-bottom: 5px;
            padding: 4px;
            border: 1px solid #8b4513;
            border-radius: 3px;
            background-color: rgba(245, 231, 193, 0.5);
        }

        .question-header {
            margin-top: 0;
            margin-bottom: 3px;
            padding-bottom: 2px;
            border-bottom: 1px solid #8b4513;
            font-size: 12px;
        }

        .overall-result {
            font-size: 13px;
            font-weight: bold;
            margin-bottom: 5px;
            padding: 5px;
            background-color: rgba(245, 231, 193, 0.7);
            border-radius: 3px;
            text-align: center;
        }

        .question-results {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
            gap: 3px;
        }

        .question-result {
            padding: 3px;
            background-color: rgba(245, 231, 193, 0.5);
            border-radius: 3px;
            border: 1px solid #8b4513;
            font-size: 10px;
        }

        .crossword-letters {
            display: flex;
            flex-wrap: wrap;
            justify-content: center;
            margin: 5px auto;
            max-width: 98%;
            gap: 3px;
        }

        .letter-tile {
            width: 26px;
            height: 26px;
            border: 1px solid #8b4513;
            border-radius: 3px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
            font-weight: bold;
            background-color: #f5e7c1;
            cursor: grab;
            transition: all 0.2s;
            user-select: none;
        }

        .letter-tile.dragging {
            opacity: 0.6;
            cursor: grabbing;
            transform: scale(1.1);
            z-index: 1000;
        }

        .letter-tile:hover {
            transform: translateY(-2px);
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        }

        .letter-tile.used {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .crossword-clues {
            margin: 5px auto;
            text-align: right;
            max-width: 98%;
            font-size: 11px;
        }

        .crossword-clue {
            margin-bottom: 3px;
            padding: 4px;
            background-color: rgba(245, 231, 193, 0.5);
            border-radius: 3px;
            border: 1px solid #8b4513;
        }

        .crossword-clue.active {
            background-color: rgba(147, 112, 219, 0.2); /* Light purple */
            border: 1px solid #9370DB;
        }

        .crossword-controls {
            margin: 8px auto;
        }
    </style>
</head>
<body>
    <div class="main-title">
        <div class="scroll-icon">📜</div>
        <h1 class="site-title">اختبار البكالوريا</h1>
    </div>
    <!-- Main Menu -->
    <section id="main-menu" class="section active">
        <div class="scroll-top"></div>
        <div class="menu-container">
            <div class="scroll-decoration left"></div>
            <div class="scroll-decoration right"></div>
            <h1>اختر المادة</h1>

            <div class="category-container">
                <h2 class="category-title">الجغرافيا</h2>
                <button class="menu-button" onclick="showSection('geography')">الجزء 1</button>
                <button class="menu-button" onclick="showSection('geo-crossword')">الجزء 1 (لعبة الكلمات المتقاطعة)</button>
            </div>

            <div class="category-container">
                <h2 class="category-title">التاريخ</h2>
                <button class="menu-button" onclick="showSection('history', 1)">الجزء 1</button>
                <button class="menu-button" onclick="showSection('history', 2)">الجزء 2</button>
                <button class="menu-button" onclick="showSection('history', 3)">الجزء 3</button>
                <button class="menu-button" onclick="showSection('history', 4)">الجزء 4</button>
                <button class="menu-button" onclick="showSection('history', 5)">الجزء 5</button>
                <button class="menu-button" onclick="showSection('history', 6)">الجزء 6</button>
                <button class="menu-button" onclick="showSection('history', 7)">الجزء 7</button>
                <button class="menu-button" onclick="showSection('history', 8)">الجزء 8</button>
                <button class="menu-button" onclick="showSection('history', 9)">الجزء 9</button>
                <button class="menu-button" onclick="showSection('history', 10)">الجزء 10</button>
                <button class="menu-button" onclick="showSection('history', 0)">كل الأجزاء</button>
            </div>

            <div class="category-container">
                <h2 class="category-title">علوم الطبيعة والحياة</h2>
                <button class="menu-button" onclick="showSection('science-unit1')">الوحدة 1</button>
                <div class="subcategory-container">
                    <h3 class="subcategory-title">الوحدة 2</h3>
                    <button class="menu-button submenu-button" onclick="showSection('science-unit2-lesson1')">درس 1</button>
                    <button class="menu-button submenu-button" onclick="showSection('science-unit2-lesson2')">درس 2</button>
                    <button class="menu-button submenu-button" onclick="showSection('science-unit2-lesson3')">درس 3</button>
                    <button class="menu-button submenu-button" onclick="showSection('science-unit2-lesson4')">درس 4</button>
                    <button class="menu-button submenu-button" onclick="showSection('science-unit2-lesson5')">درس 5</button>
                </div>
            </div>

            <div class="category-container">
                <h2 class="category-title">الفيزياء</h2>
                <div class="subcategory-container">
                    <h3 class="subcategory-title">الوحدة 1</h3>
                    <button class="menu-button submenu-button" onclick="showSection('physics-unit1-part1')">الجزء 1</button>
                </div>
                <div class="subcategory-container">
                    <h3 class="subcategory-title">الوحدة 2</h3>
                    <button class="menu-button submenu-button" onclick="showSection('physics-unit2-part1')">الجزء 1</button>
                </div>
                <div class="subcategory-container">
                    <h3 class="subcategory-title">الوحدة 3</h3>
                    <button class="menu-button submenu-button" onclick="showSection('physics-unit3')" disabled>قريباً</button>
                </div>
                <div class="subcategory-container">
                    <h3 class="subcategory-title">الوحدة 4</h3>
                    <button class="menu-button submenu-button" onclick="showSection('physics-unit4')" disabled>قريباً</button>
                </div>
                <div class="subcategory-container">
                    <h3 class="subcategory-title">الوحدة 5</h3>
                    <button class="menu-button submenu-button" onclick="showSection('physics-unit5')" disabled>قريباً</button>
                </div>
                <div class="subcategory-container">
                    <h3 class="subcategory-title">الوحدة 6</h3>
                    <button class="menu-button submenu-button" onclick="showSection('physics-unit6')" disabled>قريباً</button>
                </div>
            </div>

            <div class="category-container">
                <h2 class="category-title">الفلسفة</h2>
                <div class="subcategory-container">
                    <h3 class="subcategory-title">الحتمية/اللاحتمية</h3>
                    <button class="menu-button submenu-button" onclick="showSection('philosophy-determinism', 1)">الحجج الحتمية</button>
                    <button class="menu-button submenu-button" onclick="showSection('philosophy-determinism', 2)">الحجج اللاحتمية</button>
                    <button class="menu-button submenu-button" onclick="showSection('philosophy-determinism', 0)">كل الأجزاء</button>
                </div>
            </div>

            <div class="category-container">
                <h2 class="category-title">الشريعة الإسلامية</h2>
                <div class="subcategory-container">
                    <h3 class="subcategory-title">الوحدة 1</h3>
                    <button class="menu-button submenu-button" onclick="showSection('islamic-law-unit1-part1')">الجزء 1</button>
                </div>
            </div>
        </div>
        <div class="scroll-bottom"></div>
    </section>

    <!-- Geography Quiz -->
    <section id="geography" class="section">
        <button class="back-button" onclick="showSection('main-menu')">عودة للقائمة</button>
        <div class="scroll-top"></div>
        <div class="quiz-container" style="position: relative;">
            <div class="scroll-decoration left"></div>
            <div class="scroll-decoration right"></div>
            <p id="geo-progress" class="progress-indicator"></p>
            <div id="geo-question-block">
                <div style="display: flex; justify-content: space-between; align-items: center;">
                    <h2 id="geo-question" style="margin-right: 10px;"></h2>
                    <button id="geo-skip" style="margin-right: 10px;">تخطي السؤال</button>
                </div>
                <p id="geo-question-text"></p>
            </div>
            <div id="geo-answers-block">
                <div class="answer-input-container">
                    <textarea id="answer-input" rows="3" placeholder="اكتب إجابتك هنا..."></textarea>
                </div>
            </div>
            <div style="display: flex; justify-content: space-between; margin-bottom: 10px;">
                <div>
                    <button id="geo-submit">إرسال الإجابة</button>
                    <button id="geo-continue" style="display: none;">التالي</button>
                    <button id="geo-hint">تلميح</button>
                    <button id="geo-retry" style="display: none;">إعادة السؤال</button>
                    <button id="geo-reset">إعادة الاختبار</button>
                </div>
            </div>
            <p id="geo-result"></p>
        </div>
        <div class="scroll-bottom"></div>
    </section>

    <!-- Geography Crossword -->
    <section id="geo-crossword" class="section">
        <button class="back-button" onclick="showSection('main-menu')">عودة للقائمة</button>
        <div class="scroll-top"></div>
        <div class="quiz-container" style="position: relative;">
            <div class="scroll-decoration left"></div>
            <div class="scroll-decoration right"></div>
            <p id="crossword-progress" class="progress-indicator"></p>
            <div id="crossword-question-block">
                <h2>لعبة الكلمات المتقاطعة - الجغرافيا</h2>
            </div>

            <div class="crossword-container">
                <div class="crossword-clues" id="crossword-clues"></div>
                <div class="crossword-grid-container">
                    <div class="crossword-grid" id="crossword-grid"></div>
                    <div class="question-images" id="question-images"></div>
                </div>
                <div class="crossword-letters" id="crossword-letters"></div>
            </div>

            <div class="crossword-controls">
                <button id="crossword-check">تحقق من الإجابة</button>
                <button id="crossword-clear">مسح الإجابة</button>
                <button id="crossword-skip">تخطي السؤال</button>
                <button id="crossword-continue" style="display: none;">التالي</button>
                <button id="crossword-reset">إعادة اللعبة</button>
            </div>

            <p id="crossword-result"></p>
        </div>
        <div class="scroll-bottom"></div>
    </section>

    <!-- Science Unit 2, Lesson 1 -->
    <section id="science-unit2-lesson1" class="section">
        <button class="back-button" onclick="showSection('main-menu')">عودة للقائمة</button>
        <div class="scroll-top"></div>
        <div class="quiz-container">
            <div class="scroll-decoration left"></div>
            <div class="scroll-decoration right"></div>
            <h2>علوم الطبيعة والحياة - الوحدة 2 - الدرس 1</h2>
            <p>هذا الدرس قيد الإنشاء. سيتم إضافة محتوى تعليمي قريباً.</p>
            <div class="placeholder-content">
                <img src="https://upload.wikimedia.org/wikipedia/commons/thumb/8/8a/Diversity_of_plants_image_version_5.png/320px-Diversity_of_plants_image_version_5.png" alt="علوم النبات" style="max-width: 300px; margin: 20px auto; display: block;">
                <p style="text-align: center;">الرسم التوضيحي: تنوع النباتات</p>
            </div>
        </div>
        <div class="scroll-bottom"></div>
    </section>

    <!-- Science Unit 2, Lesson 2 -->
    <section id="science-unit2-lesson2" class="section">
        <button class="back-button" onclick="showSection('main-menu')">عودة للقائمة</button>
        <div class="scroll-top"></div>
        <div class="quiz-container">
            <div class="scroll-decoration left"></div>
            <div class="scroll-decoration right"></div>
            <h2>علوم الطبيعة والحياة - الوحدة 2 - الدرس 2</h2>
            <p>هذا الدرس قيد الإنشاء. سيتم إضافة محتوى تعليمي قريباً.</p>
            <div class="placeholder-content">
                <img src="https://upload.wikimedia.org/wikipedia/commons/thumb/0/08/Diversity_of_animals_ib.jpg/320px-Diversity_of_animals_ib.jpg" alt="علوم الحيوان" style="max-width: 300px; margin: 20px auto; display: block;">
                <p style="text-align: center;">الرسم التوضيحي: تنوع الحيوانات</p>
            </div>
        </div>
        <div class="scroll-bottom"></div>
    </section>

    <!-- Science Unit 2, Lesson 4 (Quiz) -->
    <section id="science-unit2-lesson4" class="section">
        <button class="back-button" onclick="showSection('main-menu')">عودة للقائمة</button>
        <div class="scroll-top"></div>
        <div class="quiz-container" style="position: relative;">
            <div class="scroll-decoration left"></div>
            <div class="scroll-decoration right"></div>
            <p id="bio-progress" class="progress-indicator"></p>
            <h2>علوم الطبيعة والحياة - الوحدة 2 - الدرس 4</h2>
            <div id="bio-image-container" class="placeholder-content" style="float: left; margin: 0 20px;">
                <img id="bio-image" src="https://upload.wikimedia.org/wikipedia/commons/thumb/c/c9/Protein_domains.svg/320px-Protein_domains.svg.png" alt="المستوى البنائي الثالثي" style="max-width: 200px; margin: 10px auto; display: block;">
                <p id="bio-image-caption" style="text-align: center;">الرسم التوضيحي: المستوى البنائي الثالثي للبروتين</p>
            </div>

            <div id="bio-question-block">
                <h3 id="bio-question"></h3>
                <div id="bio-answers-block">
                    <div class="multi-answer-container">
                        <div class="answer-section">
                            <label id="bio-answer1-label" class="answer-label">الإجابة الأولى</label>
                            <textarea id="bio-answer1-input" class="bio-answer-input" placeholder="اكتب إجابتك هنا..."></textarea>
                            <div id="bio-result1" class="answer-result"></div>
                        </div>
                        <div class="answer-section">
                            <label id="bio-answer2-label" class="answer-label">الإجابة الثانية</label>
                            <textarea id="bio-answer2-input" class="bio-answer-input" placeholder="اكتب إجابتك هنا..."></textarea>
                            <div id="bio-result2" class="answer-result"></div>
                        </div>
                    </div>
                    <div class="button-container">
                        <button id="bio-submit">إرسال الإجابة</button>
                        <button id="bio-hint">تلميح</button>
                        <button id="bio-learn">عرض الإجابة/تعلم</button>
                        <button id="bio-skip">تخطي السؤال</button>
                        <button id="bio-retry" style="display: none;">إعادة السؤال</button>
                        <button id="bio-continue" style="display: none;">التالي</button>
                        <button id="bio-reset">إعادة الاختبار</button>
                    </div>
                </div>
                <p id="bio-result"></p>
            </div>
        </div>
        <div class="scroll-bottom"></div>
    </section>

    <!-- Science Unit 2, Lesson 3 -->
    <section id="science-unit2-lesson3" class="section">
        <button class="back-button" onclick="showSection('main-menu')">عودة للقائمة</button>
        <div class="scroll-top"></div>
        <div class="quiz-container">
            <div class="scroll-decoration left"></div>
            <div class="scroll-decoration right"></div>
            <h2>علوم الطبيعة والحياة - الوحدة 2 - الدرس 3</h2>
            <p>هذا الدرس قيد الإنشاء. سيتم إضافة محتوى تعليمي قريباً.</p>
            <div class="placeholder-content">
                <img src="https://upload.wikimedia.org/wikipedia/commons/thumb/3/32/EscherichiaColi_NIAID.jpg/320px-EscherichiaColi_NIAID.jpg" alt="علوم المجهريات" style="max-width: 300px; margin: 20px auto; display: block;">
                <p style="text-align: center;">الرسم التوضيحي: الكائنات الدقيقة</p>
            </div>
        </div>
        <div class="scroll-bottom"></div>
    </section>

    <!-- Science Unit 2, Lesson 5 -->
    <section id="science-unit2-lesson5" class="section">
        <button class="back-button" onclick="showSection('main-menu')">عودة للقائمة</button>
        <div class="scroll-top"></div>
        <div class="quiz-container">
            <div class="scroll-decoration left"></div>
            <div class="scroll-decoration right"></div>
            <h2>علوم الطبيعة والحياة - الوحدة 2 - الدرس 5</h2>
            <p>هذا الدرس قيد الإنشاء. سيتم إضافة محتوى تعليمي قريباً.</p>
            <div class="placeholder-content">
                <img src="https://upload.wikimedia.org/wikipedia/commons/thumb/f/f3/Human_Genome_and_Genes.png/320px-Human_Genome_and_Genes.png" alt="علوم الوراثة" style="max-width: 300px; margin: 20px auto; display: block;">
                <p style="text-align: center;">الرسم التوضيحي: الجينات والوراثة</p>
            </div>
        </div>
        <div class="scroll-bottom"></div>
    </section>

    <!-- Physics Unit 1, Part 1 -->
    <section id="physics-unit1-part1" class="section">
        <button class="back-button" onclick="showSection('main-menu')">عودة للقائمة</button>
        <div class="scroll-top"></div>
        <div class="quiz-container">
            <div class="scroll-decoration left"></div>
            <div class="scroll-decoration right"></div>
            <h2>الفيزياء - الوحدة 1 - الجزء 1: تفاعلات الأكسدة والاختزال</h2>

            <div class="physics-content">
                <div class="physics-image-container">
                    <img src="https://upload.wikimedia.org/wikipedia/commons/thumb/d/d5/Redox_reaction_of_copper_and_silver_nitrate.jpg/320px-Redox_reaction_of_copper_and_silver_nitrate.jpg" alt="تفاعل أكسدة واختزال" style="max-width: 300px; margin: 20px auto; display: block;">
                    <p style="text-align: center;">تفاعل أكسدة واختزال بين النحاس ونترات الفضة</p>
                </div>

                <div class="physics-text">
                    <h3>مقدمة في تفاعلات الأكسدة والاختزال</h3>
                    <p>تفاعلات الأكسدة والاختزال (Redox) هي تفاعلات كيميائية تتضمن انتقال الإلكترونات بين المواد المتفاعلة. تحدث الأكسدة والاختزال دائماً معاً، حيث لا يمكن أن تحدث أكسدة دون اختزال.</p>

                    <ul>
                        <li><strong>الأكسدة (Oxidation):</strong> هي فقدان الإلكترونات أو زيادة عدد التأكسد.</li>
                        <li><strong>الاختزال (Reduction):</strong> هو اكتساب الإلكترونات أو نقصان عدد التأكسد.</li>
                    </ul>

                    <h3>أنصاف المعادلات</h3>
                    <p>يمكن تقسيم تفاعل الأكسدة والاختزال إلى نصفي تفاعل:</p>

                    <ol>
                        <li><strong>نصف تفاعل الأكسدة:</strong> يوضح فقدان الإلكترونات.</li>
                        <li><strong>نصف تفاعل الاختزال:</strong> يوضح اكتساب الإلكترونات.</li>
                    </ol>

                    <div class="physics-example">
                        <p><strong>مثال:</strong> تفاعل الزنك مع محلول كبريتات النحاس:</p>
                        <p class="physics-formula">Zn(s) + CuSO₄(aq) → ZnSO₄(aq) + Cu(s)</p>
                        <p>يمكن تقسيمه إلى:</p>
                        <p class="physics-formula">نصف تفاعل الأكسدة: Zn(s) → Zn²⁺(aq) + 2e⁻</p>
                        <p class="physics-formula">نصف تفاعل الاختزال: Cu²⁺(aq) + 2e⁻ → Cu(s)</p>
                    </div>

                    <h3>تمارين تفاعلية</h3>
                    <p>جرب التمارين التالية لتطبيق ما تعلمته عن تفاعلات الأكسدة والاختزال:</p>

                    <!-- Exercise 1: Half-Equations -->
                    <div class="redox-exercise">
                        <h4>التمرين 1: كتابة أنصاف المعادلات</h4>
                        <p>قم بسحب العناصر لتكوين نصفي معادلة الأكسدة والاختزال للتفاعل التالي:</p>
                        <p class="physics-formula">Fe(s) + CuSO₄(aq) → FeSO₄(aq) + Cu(s)</p>

                        <div class="redox-workspace">
                            <div class="redox-target-container">
                                <div class="redox-target-label">نصف معادلة الأكسدة (Oxidation):</div>
                                <div class="redox-target" id="oxidation-half-equation-target" dir="ltr">
                                    <p>اسحب العناصر هنا لتكوين نصف معادلة الأكسدة</p>
                                </div>

                                <div class="redox-target-label">نصف معادلة الاختزال (Reduction):</div>
                                <div class="redox-target" id="reduction-half-equation-target" dir="ltr">
                                    <p>اسحب العناصر هنا لتكوين نصف معادلة الاختزال</p>
                                </div>
                            </div>

                            <div class="redox-pieces" id="half-equation-pieces">
                                <div class="redox-piece" draggable="true" data-value="Fe(s)">Fe(s)</div>
                                <div class="redox-piece" draggable="true" data-value="→">→</div>
                                <div class="redox-piece" draggable="true" data-value="Fe">Fe</div>
                                <div class="redox-piece" draggable="true" data-value="²⁺">²⁺</div>
                                <div class="redox-piece" draggable="true" data-value="³⁺">³⁺</div>
                                <div class="redox-piece" draggable="true" data-value="(aq)">(aq)</div>
                                <div class="redox-piece" draggable="true" data-value="+">+</div>
                                <div class="redox-piece" draggable="true" data-value="2">2</div>
                                <div class="redox-piece" draggable="true" data-value="e⁻">e⁻</div>
                                <div class="redox-piece" draggable="true" data-value="Cu²⁺(aq)">Cu²⁺(aq)</div>
                                <div class="redox-piece" draggable="true" data-value="Cu²⁺">Cu²⁺</div>
                                <div class="redox-piece" draggable="true" data-value="Cu(s)">Cu(s)</div>
                                <div class="redox-piece" draggable="true" data-value="Cu">Cu</div>
                                <div class="redox-piece" draggable="true" data-value="H⁺">H⁺</div>
                                <div class="redox-piece" draggable="true" data-value="OH⁻">OH⁻</div>
                                <div class="redox-piece" draggable="true" data-value="H₂O">H₂O</div>
                                <div class="redox-piece" draggable="true" data-value="SO₄²⁻">SO₄²⁻</div>
                            </div>

                            <div class="redox-feedback" id="half-equation-feedback"></div>

                            <div class="redox-buttons">
                                <button id="check-half-equation">تحقق من الإجابة</button>
                                <button id="reset-half-equation">إعادة ضبط</button>
                            </div>
                        </div>
                    </div>

                    <!-- Exercise 2: Redox Pairs -->
                    <div class="redox-exercise">
                        <h4>التمرين 2: حدد الثنائيات المشاركة في التفاعل (Ox/Red)</h4>
                        <p>في التفاعل التالي، حدد الثنائيات المشاركة في التفاعل بوضع المؤكسد على اليسار والمرجع على اليمين:</p>
                        <p class="physics-formula">Fe(s) + CuSO₄(aq) → FeSO₄(aq) + Cu(s)</p>

                        <div class="redox-workspace">
                            <div class="redox-target-container">
                                <div class="redox-target-label">الثنائية الأولى (First Redox Pair):</div>
                                <div class="redox-pair-container">
                                    <div class="redox-target redox-ox" id="oxidizing-agent-target" dir="ltr">
                                        <p>المؤكسد (Ox)</p>
                                    </div>
                                    <div class="redox-separator">/</div>
                                    <div class="redox-target redox-red" id="reducing-agent-target" dir="ltr">
                                        <p>المرجع (Red)</p>
                                    </div>
                                </div>
                            </div>

                            <div class="redox-pieces" id="agents-pieces">
                                <div class="redox-piece" draggable="true" data-value="Fe(s)">Fe(s)</div>
                                <div class="redox-piece" draggable="true" data-value="CuSO₄(aq)">CuSO₄(aq)</div>
                                <div class="redox-piece" draggable="true" data-value="Cu²⁺(aq)">Cu²⁺(aq)</div>
                                <div class="redox-piece" draggable="true" data-value="FeSO₄(aq)">FeSO₄(aq)</div>
                                <div class="redox-piece" draggable="true" data-value="Cu(s)">Cu(s)</div>
                                <div class="redox-piece" draggable="true" data-value="Fe²⁺(aq)">Fe²⁺(aq)</div>
                            </div>

                            <div class="redox-feedback" id="agents-feedback"></div>

                            <div class="redox-buttons">
                                <button id="check-agents">تحقق من الإجابة</button>
                                <button id="reset-agents">إعادة ضبط</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="scroll-bottom"></div>
    </section>

    <!-- Physics Unit 2, Part 1 -->
    <section id="physics-unit2-part1" class="section">
        <button class="back-button" onclick="showSection('main-menu')">عودة للقائمة</button>
        <div class="scroll-top"></div>
        <div class="quiz-container" style="position: relative;">
            <div class="scroll-decoration left"></div>
            <div class="scroll-decoration right"></div>
            <p id="phys2-progress" class="progress-indicator"></p>
            <h2>الفيزياء - الوحدة 2 - الجزء 1: المراجع وأنواعها والأجسام الصلبة</h2>

            <div id="phys2-question-block">
                <h2 id="phys2-question"></h2>
            </div>
            <div id="phys2-answers-block">
                <div class="answer-input-container">
                    <textarea id="phys2-answer-input" rows="3" placeholder="اكتب إجابتك هنا..."></textarea>
                </div>
            </div>
            <button id="phys2-submit">تحقق من الإجابة</button>
            <button id="phys2-skip">تخطي السؤال</button>
            <button id="phys2-prev" style="display: none;">السؤال السابق</button>
            <button id="phys2-retry" style="display: none;">إعادة السؤال</button>
            <button id="phys2-continue" style="display: none;">التالي</button>
            <button id="phys2-hint">تلميح</button>
            <button id="phys2-learn">عرض الإجابة/تعلم</button>
            <button id="phys2-reset">إعادة الاختبار</button>
            <p id="phys2-result"></p>
        </div>
        <div class="scroll-bottom"></div>
    </section>

    <!-- Science Unit 1 -->
    <section id="science-unit1" class="section">
        <button class="back-button" onclick="showSection('main-menu')">عودة للقائمة</button>
        <div class="scroll-top"></div>
        <div class="quiz-container">
            <div class="scroll-decoration left"></div>
            <div class="scroll-decoration right"></div>
            <h2>علوم الطبيعة والحياة - الوحدة 1</h2>
            <p>هذه الوحدة قيد الإنشاء. سيتم إضافة محتوى تعليمي قريباً.</p>
            <div class="placeholder-content">
                <img src="https://upload.wikimedia.org/wikipedia/commons/thumb/d/d9/Biological_classification_L_Pengo_vflip.svg/320px-Biological_classification_L_Pengo_vflip.svg.png" alt="علوم الطبيعة والحياة" style="max-width: 300px; margin: 20px auto; display: block;">
                <p style="text-align: center;">الرسم التوضيحي: تصنيف الكائنات الحية</p>
            </div>
        </div>
        <div class="scroll-bottom"></div>
    </section>

    <!-- History Quiz -->
    <section id="history" class="section">
        <button class="back-button" onclick="showSection('main-menu')">عودة للقائمة</button>
        <div class="scroll-top"></div>
        <div class="quiz-container" style="position: relative;">
            <div class="scroll-decoration left"></div>
            <div class="scroll-decoration right"></div>
            <p id="hist-progress" class="progress-indicator"></p>
            <div id="hist-question-block">
                <h2 id="hist-question"></h2>
            </div>
            <div id="hist-answers-block">
                <div id="date-inputs"></div>
            </div>
            <button id="hist-submit">تحقق من الإجابة</button>
            <button id="hist-skip">تخطي السؤال</button>
            <button id="hist-prev" style="display: none;">السؤال السابق</button>
            <button id="hist-retry" style="display: none;">إعادة السؤال</button>
            <button id="hist-continue" style="display: none;">التالي</button>
            <button id="hist-reset">إعادة الاختبار</button>
            <p id="hist-result"></p>
        </div>
        <div class="scroll-bottom"></div>
    </section>

    <!-- Philosophy Quiz - Determinism/Indeterminism -->
    <section id="philosophy-determinism" class="section">
        <button class="back-button" onclick="showSection('main-menu')">عودة للقائمة</button>
        <div class="scroll-top"></div>
        <div class="quiz-container" style="position: relative;">
            <div class="scroll-decoration left"></div>
            <div class="scroll-decoration right"></div>
            <p id="phil-progress" class="progress-indicator"></p>
            <div id="phil-question-block">
                <h2 id="phil-question"></h2>
            </div>
            <div id="phil-answers-block">
                <div class="answer-input-container">
                    <textarea id="phil-answer-input" rows="3" placeholder="اكتب إجابتك هنا..."></textarea>
                </div>
            </div>
            <button id="phil-submit">تحقق من الإجابة</button>
            <button id="phil-skip">تخطي السؤال</button>
            <button id="phil-prev" style="display: none;">السؤال السابق</button>
            <button id="phil-retry" style="display: none;">إعادة السؤال</button>
            <button id="phil-continue" style="display: none;">التالي</button>
            <button id="phil-hint">تلميح</button>
            <button id="phil-learn">عرض الإجابة/تعلم</button>
            <button id="phil-reset">إعادة الاختبار</button>
            <p id="phil-result"></p>
        </div>
        <div class="scroll-bottom"></div>
    </section>

    <!-- Islamic Law Unit 1 Part 1 -->
    <section id="islamic-law-unit1-part1" class="section">
        <button class="back-button" onclick="showSection('main-menu')">عودة للقائمة</button>
        <div class="scroll-top"></div>
        <div class="quiz-container" style="position: relative;">
            <div class="scroll-decoration left"></div>
            <div class="scroll-decoration right"></div>
            <p id="islamic-progress" class="progress-indicator"></p>
            <h2>الشريعة الإسلامية - الوحدة 1 - الجزء 1: العقيدة الإسلامية</h2>

            <div id="islamic-question-block">
                <h2 id="islamic-question"></h2>
            </div>
            <div id="islamic-answers-block">
                <div class="answer-input-container">
                    <textarea id="islamic-answer-input" rows="3" placeholder="اكتب إجابتك هنا..."></textarea>
                </div>
            </div>
            <div style="display: flex; justify-content: space-between; margin-bottom: 10px;">
                <div>
                    <button id="islamic-submit">تحقق من الإجابة</button>
                    <button id="islamic-continue" style="display: none;">التالي</button>
                    <button id="islamic-hint">تلميح</button>
                    <button id="islamic-retry" style="display: none;">إعادة السؤال</button>
                    <button id="islamic-reset">إعادة الاختبار</button>
                </div>
                <div>
                    <button id="islamic-skip">تخطي السؤال</button>
                    <button id="islamic-learn">عرض الإجابة/تعلم</button>
                </div>
            </div>
            <p id="islamic-result"></p>
        </div>
        <div class="scroll-bottom"></div>
    </section>

    <script>
        // Navigation
        function showSection(sectionId, part = 0) {
            document.querySelectorAll('.section').forEach(section => {
                section.classList.remove('active');
            });
            document.getElementById(sectionId).classList.add('active');

            if (sectionId === 'geography') {
                loadGeographyQuestion();
            } else if (sectionId === 'history') {
                // Set the current history part based on the parameter
                loadHistoryPart(part);
                loadHistoryQuestion();
            } else if (sectionId === 'philosophy-determinism') {
                // Set the current philosophy part based on the parameter
                loadPhilosophyPart(part);
                loadPhilosophyQuestion();
            } else if (sectionId === 'physics-unit2-part1') {
                // Load physics unit 2 questions
                loadPhysicsUnit2Question();
            } else if (sectionId === 'islamic-law-unit1-part1') {
                // Load Islamic Law unit 1 part 1 questions
                loadIslamicLawQuestion();
            }
        }

        // Function to load a specific part of history questions
        function loadHistoryPart(part) {
            // Reset score and current question
            histScore = 0;
            currentHistQuestion = 0;
            currentHistoryPart = part; // Track the current part

            // Load the appropriate part
            switch(part) {
                case 1:
                    historyEvents = shuffleArray(historyEventsPart1);
                    document.getElementById("hist-question").textContent = "الجزء 1";
                    break;
                case 2:
                    historyEvents = shuffleArray(historyEventsPart2);
                    document.getElementById("hist-question").textContent = "الجزء 2";
                    break;
                case 3:
                    historyEvents = shuffleArray(historyEventsPart3);
                    document.getElementById("hist-question").textContent = "الجزء 3";
                    break;
                case 4:
                    historyEvents = shuffleArray(historyEventsPart4);
                    document.getElementById("hist-question").textContent = "الجزء 4";
                    break;
                case 5:
                    historyEvents = shuffleArray(historyEventsPart5);
                    document.getElementById("hist-question").textContent = "الجزء 5";
                    break;
                case 6:
                    historyEvents = shuffleArray(historyEventsPart6);
                    document.getElementById("hist-question").textContent = "الجزء 6";
                    break;
                case 7:
                    historyEvents = shuffleArray(historyEventsPart7);
                    document.getElementById("hist-question").textContent = "الجزء 7";
                    break;
                case 8:
                    historyEvents = shuffleArray(historyEventsPart8);
                    document.getElementById("hist-question").textContent = "الجزء 8";
                    break;
                case 9:
                    historyEvents = shuffleArray(historyEventsPart9);
                    document.getElementById("hist-question").textContent = "الجزء 9";
                    break;
                case 10:
                    historyEvents = shuffleArray(historyEventsPart10);
                    document.getElementById("hist-question").textContent = "الجزء 10";
                    break;
                default:
                    // Load all parts
                    historyEvents = shuffleArray(historyEventsOriginal);
                    document.getElementById("hist-question").textContent = "كل الأجزاء";
                    break;
            }
        }

        // Function to load a specific part of philosophy questions
        function loadPhilosophyPart(part) {
            // Reset score and current question
            philScore = 0;
            currentPhilQuestion = 0;
            currentPhilosophyPart = part; // Track the current part

            // Load the appropriate part
            switch(part) {
                case 1:
                    philosophyQuestions = shuffleArray(philosophyDeterministicPart);
                    document.getElementById("phil-question").textContent = "الحجج الحتمية";
                    break;
                case 2:
                    philosophyQuestions = shuffleArray(philosophyIndeterministicPart);
                    document.getElementById("phil-question").textContent = "الحجج اللاحتمية";
                    break;
                default:
                    // Load all parts
                    philosophyQuestions = shuffleArray(philosophyQuestionsOriginal);
                    document.getElementById("phil-question").textContent = "كل الأجزاء";
                    break;
            }
        }

        // Geography Quiz Logic
        const geoQuestionsOriginal = [
            {
                question: "تبييض الأموال",
                definition: "إخفاء المصدر الحقيقي غير المشروع للأموال, ظهر هذا المصطلح في الو.م.أ سنة 1988 ويعني إعادة رسكلة الأموال غير المشروعة الناتجة عن تجارة (المخدرات-الأسلحة-الوثائق المزورة)."
            },
            {
                question: "المضاربة",
                definition: "هي المخاطرة بالبيع والشراء بناءاََ على توقع تقلبات أسعار السوق"
            },
            {
                question: "التنمية",
                definition: "حركة تطوير المجالات المختلفة, باستغلال رؤوس الأموال والتحهيزات والإمكانات البشرية وهي متعددة الجوانب: البشرية,الإقتصادية, الإجتماعية..."
            },
            {
                question: "المعيار",
                definition: "هو المقياس المعتمد من المنظمات والهيئات التابعة للأمم المتحدة لتصنييف الدول متقدمة أو متخلفة."
            },
            {
                question: "المؤشر",
                definition: "هو الدليل الرقمي أو الإحصائيات والنسب  التي تثبت وتوضح واقع ظاهرة معينة كنسبة النمو الديمغرافي أو كمية الإنتاج أو متوسط العمر ونسبة المساهمة في التجارة الدولية (أرقام)"
            }
        ];

        // Philosophy Quiz Logic
        // Part 1: Deterministic Arguments
        const philosophyDeterministicPart = [
            {
                question: "ماذا قال نيوتن دفاعا عن الحتمية؟",
                answer: "إذا علمنا موقع جسم ما وحركته أمكننا التنبؤ بمساره"
            },
            {
                question: "ما هي الحجة الفيزيائية للحتمية؟",
                answer: "كل الظواهر الطبيعية تخضع لقوانين فيزيائية صارمة تحكم حركة الجسيمات وتفاعلاتها"
            },
            {
                question: "كيف دافع سبينوزا عن الحتمية؟",
                answer: "لا يوجد شيء في الطبيعة عشوائي، بل كل شيء محدد بضرورة الطبيعة الإلهية ليوجد ويعمل بطريقة معينة"
            },
            {
                question: "ما هي الحجة البيولوجية للحتمية؟",
                answer: "سلوكنا محدد بالعوامل الوراثية والجينية التي لا نملك سيطرة عليها"
            },
            {
                question: "ما هي الحجة النفسية للحتمية؟",
                answer: "أفعالنا محددة بدوافعنا اللاواعية وتجاربنا السابقة التي تشكل شخصيتنا"
            }
        ];

        // Part 2: Indeterministic Arguments
        const philosophyIndeterministicPart = [
            {
                question: "ما هي حجة الكوانتم ضد الحتمية؟",
                answer: "ميكانيكا الكم تظهر أن بعض الظواهر الذرية عشوائية بطبيعتها ولا يمكن التنبؤ بها بدقة"
            },
            {
                question: "كيف دافع سارتر عن اللاحتمية؟",
                answer: "الإنسان محكوم عليه بالحرية، فهو يخلق ماهيته من خلال اختياراته الحرة"
            },
            {
                question: "ما هي حجة الإرادة الحرة ضد الحتمية؟",
                answer: "نمتلك القدرة على الاختيار بين بدائل مختلفة، وهذا يتعارض مع فكرة أن كل شيء محدد مسبقاً"
            },
            {
                question: "ما هي الحجة الأخلاقية ضد الحتمية؟",
                answer: "المسؤولية الأخلاقية تفترض حرية الاختيار، فلا معنى للثواب والعقاب إذا كانت أفعالنا محددة مسبقاً"
            },
            {
                question: "كيف دافع كانط عن اللاحتمية؟",
                answer: "الإنسان ينتمي إلى عالمين: عالم الظواهر الخاضع للحتمية، وعالم الأشياء في ذاتها حيث الحرية ممكنة"
            }
        ];

        // Combine all parts for the original array
        const philosophyQuestionsOriginal = [
            ...philosophyDeterministicPart,
            ...philosophyIndeterministicPart
        ];

        // Function to shuffle array (Fisher-Yates algorithm)
        function shuffleArray(array) {
            const shuffled = [...array]; // Create a copy to avoid modifying the original
            for (let i = shuffled.length - 1; i > 0; i--) {
                const j = Math.floor(Math.random() * (i + 1));
                [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]]; // Swap elements
            }
            return shuffled;
        }

        // Shuffle questions at the start
        let geoQuestions = shuffleArray(geoQuestionsOriginal);
        let currentGeoQuestion = 0;
        let geoScore = 0;

        // Philosophy Quiz variables
        let philosophyQuestions = shuffleArray(philosophyQuestionsOriginal);
        let currentPhilQuestion = 0;
        let philScore = 0;
        let philHintLevel = 0; // Track hint usage for philosophy questions
        let currentPhilosophyPart = 0; // Track which part is currently active

        // Physics Unit 2 Quiz variables
        const physicsUnit2QuestionsOriginal = [
            {
                question: "ماهو المرجع؟",
                answer: "هو كل جسم صلب تنسب إليه الحركة"
            },
            {
                question: "ماهو الجسم الصلب؟",
                answer: "هو كل جسم يحافظ على شكله أثناء الحركة"
            },
            {
                question: "ماهو شرط إستعمال المرجع؟",
                answer: "هو أن يكون المرجع عطاليا"
            },
            {
                question: "المرجع العطالي؟",
                answer: "هو كل مرجع يطبق فيه مبدأ العطالة (غاليلي)"
            },
            {
                question: "ماهو المعلم؟",
                answer: "شكل هندسي يحدد خصائص الحركة"
            },
            {
                question: "ماهو المرجع الهيليومركزي؟",
                answer: "مرجع مركزي شمسي هو مرجع مبدأ معلمه هو مركز الشمس"
            },
            {
                question: "ماهو المرجع الجيومركزي؟",
                answer: "هو مرجع مبدأ معلمه هو مركز الأرض"
            },
            {
                question: "ماهو المرجع السطحي الأرضي؟",
                answer: "هو مرجع مبدأ معلمه هو نقطة من سطح الأرض فيه 3 محاور متجهة نحو 3 نجوم نفسرها كنقطة في الفضاء"
            },
            {
                question: "ما مبدأ العطالة؟",
                answer: "يحافظ الجسم على سكونه أو حركته المستقيمة المنتظمة إذا لم تؤثر عليه قوة خارجية"
            },
            {
                question: "ما القانون الأول لنيوتن؟",
                answer: "يحافظ الجسم على سكونه أو حركته المستقيمة المنتظمة إذا لم تؤثر عليه قوة خارجية"
            }
        ];

        let physicsUnit2Questions = shuffleArray(physicsUnit2QuestionsOriginal);
        let currentPhysics2Question = 0;
        let physics2Score = 0;
        let physics2HintLevel = 0; // Track hint usage for physics unit 2 questions

        // Islamic Law Quiz variables
        const islamicLawQuestionsOriginal = [
            {
                question: "ما أسباب إنحراف العقيدة الإسلامية؟",
                answers: [
                    "الجهل بأصول العقيدة ومعانيها",
                    "التقليد الأعمى للموروثات",
                    "التشدد والغلو في الدين",
                    "الغفلة عن تدبر الآيات الكونية والقرآنية",
                    "الانغماس في الملذات والشهوات"
                ]
            }
        ];

        let islamicLawQuestions = [...islamicLawQuestionsOriginal];
        let currentIslamicQuestion = 0;
        let islamicScore = 0;
        let islamicHintLevel = 0; // Track hint usage for Islamic Law questions

        // Variables to track hint usage for the current question
        let currentQuestionHintLevel = 0; // Tracks how many times hints have been used
        let hintPenalty = 0.05; // 5% penalty for using a hint (more lenient)

        function loadGeographyQuestion() {
            const question = geoQuestions[currentGeoQuestion];
            document.getElementById("geo-question").textContent = question.question;
            document.getElementById("answer-input").value = "";
            document.getElementById("geo-result").textContent = "";
            document.getElementById("geo-continue").style.display = "none";
            document.getElementById("geo-retry").style.display = "none";
            document.getElementById("geo-submit").disabled = false;
            document.getElementById("geo-skip").disabled = false;
            document.getElementById("geo-hint").disabled = false;
            document.getElementById("answer-input").disabled = false;

            // Reset hint usage for the new question
            currentQuestionHintLevel = 0;

            // Make sure all relevant buttons are visible
            document.getElementById("geo-skip").style.display = "inline-block";
            document.getElementById("geo-submit").style.display = "inline-block";
            document.getElementById("geo-hint").style.display = "inline-block";

            // Update progress indicator
            const progressText = `السؤال ${currentGeoQuestion + 1} من ${geoQuestions.length}`;
            document.getElementById("geo-progress").textContent = progressText;
        }

        // History Quiz Logic
        // Part 1
        const historyEventsPart1 = [
            { date: "9-6 اوت 1945", event: "تفجير قنبلتي هيروشيما وناكازاكي" },
            { date: "24 اكتوبر 1945", event: "تاسيس هيىة الامم المتحدة" },
            { date: "12 مارس 1947", event: "الاعلان عن مبدا هاري ترومان" },
            { date: "5 جوان 1947", event: "الاعلان عن مشروع جورج مرشال" },
            { date: "22 سبتمبر 1947", event: "الاعلان عن مبدا جدانوف" }
        ];

        // Part 2
        const historyEventsPart2 = [
            { date: "6 اكتوبر 1947", event: "إنشاء مكتب الكومنفورم" },
            { date: "25 جانفي 1949", event: "تأسيس منظمة الكوميكون" },
            { date: "4 افريل 1949", event: "تأسيس حلف الشمال الأطلسي (الناتو)" },
            { date: "1 اكتوبر 1949", event: "انتصار الثورة الشيوعية الصينية" },
            { date: "8 سبتمبر 1954", event: "تأسيس حلف جنوب شرق آسيا (السباتو)" }
        ];

        // Part 3
        const historyEventsPart3 = [
            { date: "24 فيفري 1955", event: "تأسيس حلف بغداد" },
            { date: "14 ماي 1955", event: "تأسيس حلف وارسو" },
            { date: "17 أفريل 1956", event: "حل مكتب الكومنفورم" },
            { date: "29 اكتوبر 1956", event: "العدوان الثلاثي على مصر" },
            { date: "5 نوفمبر 1956", event: "تهديد السوفييت باستعمال الأسلحة النووية ضد دول العدوان الثلاثي" }
        ];

        // Part 4
        const historyEventsPart4 = [
            { date: "5 جانفي 1957", event: "مشروع إيزنهاور" },
            { date: "3 جوان 1948", event: "اجتماع لندن وتأسيس حكومة رأسمالية في غرب ألمانيا" },
            { date: "23 جوان 1948", event: "حصار السوفييت لبرلين" },
            { date: "25 جوان 1948", event: "إنشاء جسر جوي إلى برلين لفك الحصار عنها" },
            { date: "8 ماي 1949", event: "تأسيس ألمانيا الغربية الرأسمالية الاتحادية عاصمتها بون" }
        ];

        // Part 5
        const historyEventsPart5 = [
            { date: "12 ماي 1949", event: "رفع الحصار عن برلين" },
            { date: "7 اكتوبر 1949", event: "تأسيس ألمانيا الديمقراطية الشرقية الشيوعية عاصمتها برلين الشرقية" },
            { date: "25 جوان 1950", event: "اندلاع الحرب الكورية بغزو القسم الشمالي للجنوب" },
            { date: "5 مارس 1953", event: "وفاة الزعيم السوفياتي ستالين" },
            { date: "27 جويلية 1953", event: "نهاية الحرب الأهلية الكورية" }
        ];

        // Part 6
        const historyEventsPart6 = [
            { date: "13 أوت 1961", event: "بناء جدار برلين" },
            { date: "7 فيفري 1962", event: "فرض الحصار الاقتصادي الأمريكي على كوبا" },
            { date: "22 اكتوبر 1962", event: "فرض الحصار العسكري الأمريكي على كوبا" },
            { date: "28 أكتوبر 1962", event: "قرار سحب السوفيات للصواريخ من كوبا ونهاية الأزمة الكوبية" },
            { date: "27 ديسمبر 1979", event: "تدخل السوفيات عسكرياً في أفغانستان" }
        ];

        // Part 7
        const historyEventsPart7 = [
            { date: "14-18 أفريل 1955", event: "مؤتمر باندونغ" },
            { date: "05 ماي 1955", event: "اعتراف السوفيات بألمانيا الغربية الاتحادية" },
            { date: "14 فبراير 1956", event: "تبني خروتشوف سياسة التعايش السلمي" },
            { date: "15-18 سبتمبر 1959", event: "زيارة خروتشوف إلى واشنطن" },
            { date: "01-06 سبتمبر 1961", event: "تأسيس حركة عدم الانحياز في بلغراد، يوغوسلافيا" }
        ];

        // Part 8
        const historyEventsPart8 = [
            { date: "20 جوان 1963", event: "إنشاء خط الهاتف الأحمر بين واشنطن وموسكو" },
            { date: "26 ماي 1972", event: "اتفاقية سالت الأولى للحد من الأسلحة الاستراتيجية" },
            { date: "05-09 سبتمبر 1973", event: "المؤتمر الرابع لحركة عدم الانحياز في الجزائر" },
            { date: "18 جوان 1979", event: "اتفاقية سالت الثانية في فيينا لتحديد كمية الأسلحة والصواريخ العابرة للقارات" },
            { date: "09 نوفمبر 1989", event: "هدم جدار برلين" }
        ];

        // Part 9
        const historyEventsPart9 = [
            { date: "03 أكتوبر 1990", event: "توحيد ألمانيا" },
            { date: "19-21 نوفمبر 1990", event: "مؤتمر باريس وإعلان نهاية الحرب الباردة رسمياً" },
            { date: "03-04 ديسمبر 1989", event: "مؤتمر مالطا بين غورباتشوف وجورج بوش الأب والإعلان عن زوال القطبية الثنائية" },
            { date: "05 سبتمبر 1991", event: "حل الحزب الشيوعي السوفياتي" },
            { date: "28 جوان 1991", event: "حل منظمة الكوميكون" }
        ];

        // Part 10
        const historyEventsPart10 = [
            { date: "01 جويلية 1991", event: "حل حلف وارسو" },
            { date: "21 ديسمبر 1991", event: "مؤتمر ألما-آتا وزوال الاتحاد السوفياتي وظهور الجمهوريات المستقلة" },
            { date: "25 ديسمبر 1991", event: "استقالة غورباتشوف من رئاسة الاتحاد السوفياتي" },
            { date: "03 جانفي 1993", event: "توقيع معاهدة ستارت 2 بين روسيا والولايات المتحدة" },
            { date: "01 نوفمبر 1993", event: "دخول معاهدة ماستريخت حيز التنفيذ وتأسيس الاتحاد الأوروبي" }
        ];

        // Combine all parts for the original array
        const historyEventsOriginal = [
            ...historyEventsPart1,
            ...historyEventsPart2,
            ...historyEventsPart3,
            ...historyEventsPart4,
            ...historyEventsPart5,
            ...historyEventsPart6,
            ...historyEventsPart7,
            ...historyEventsPart8,
            ...historyEventsPart9,
            ...historyEventsPart10
        ];

        // Shuffle history questions at the start
        let historyEvents = shuffleArray(historyEventsOriginal);
        let currentHistQuestion = 0;
        let histScore = 0;
        let currentHistoryPart = 0; // Track which part is currently active

        // Array of Arabic month names for reference
        const arabicMonths = [
            "جانفي", "فيفري", "مارس", "افريل", "ماي", "جوان",
            "جويلية", "اوت", "سبتمبر", "اكتوبر", "نوفمبر", "ديسمبر"
        ];

        // Additional month name variations for matching
        const monthVariations = {
            "جانفي": ["جانفي", "يناير"],
            "فيفري": ["فيفري", "فبراير"],
            "مارس": ["مارس", "آذار"],
            "افريل": ["افريل", "أفريل", "ابريل", "أبريل"],
            "ماي": ["ماي", "مايو"],
            "جوان": ["جوان", "يونيو"],
            "جويلية": ["جويلية", "يوليو"],
            "اوت": ["اوت", "أوت", "أغسطس", "اغسطس"],
            "سبتمبر": ["سبتمبر"],
            "اكتوبر": ["اكتوبر", "أكتوبر"],
            "نوفمبر": ["نوفمبر"],
            "ديسمبر": ["ديسمبر"]
        };

        // Function to generate wrong dates that are close to the correct date
        function generateWrongDates(correctDate, count) {
            // Parse the correct date
            let [dayPart, month, year] = correctDate.split(" ");
            let day = dayPart;
            if (dayPart.includes("-")) {
                // Handle date ranges like "9-6"
                const dayParts = dayPart.split("-");
                day = Math.floor((parseInt(dayParts[0]) + parseInt(dayParts[1])) / 2).toString();
            }
            day = parseInt(day);

            // Find the standard month name by checking all variations
            let standardMonth = month;
            let monthIndex = arabicMonths.indexOf(month);

            // If month not found directly, check variations
            if (monthIndex === -1) {
                for (const [standard, variations] of Object.entries(monthVariations)) {
                    if (variations.includes(month)) {
                        standardMonth = standard;
                        monthIndex = arabicMonths.indexOf(standard);
                        break;
                    }
                }
            }

            // If still not found, default to January
            if (monthIndex === -1) {
                console.warn(`Month not recognized: ${month}, defaulting to January`);
                monthIndex = 0;
                standardMonth = arabicMonths[0];
            }

            year = parseInt(year);

            const wrongDates = [];

            // Get days in month (simplified)
            const daysInMonth = [31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31];
            // Adjust for leap years
            if (year % 4 === 0 && (year % 100 !== 0 || year % 400 === 0)) {
                daysInMonth[1] = 29;
            }

            // Generate 'count' wrong dates
            for (let i = 0; i < count; i++) {
                // Decide which part to change (day, year, or both)
                const changeType = Math.floor(Math.random() * 3); // 0: day only, 1: year only, 2: both

                let wrongDay = day;
                let wrongMonthIndex = monthIndex;
                let wrongYear = year;

                // Change the day if needed (changeType 0 or 2)
                if (changeType === 0 || changeType === 2) {
                    const dayChange = Math.floor(Math.random() * 5) + 1;
                    const dayDirection = Math.random() < 0.5 ? 1 : -1;
                    wrongDay = day + (dayChange * dayDirection);

                    // Ensure day is valid for the specific month
                    const maxDaysInMonth = daysInMonth[wrongMonthIndex];
                    if (wrongDay < 1) {
                        wrongDay = 1; // Keep it at the first day of the month
                    } else if (wrongDay > maxDaysInMonth) {
                        wrongDay = maxDaysInMonth; // Keep it at the last day of the month
                    }
                }

                // Change the year if needed (changeType 1 or 2)
                if (changeType === 1 || changeType === 2) {
                    const yearChange = Math.floor(Math.random() * 5) + 1;
                    const yearDirection = Math.random() < 0.5 ? 1 : -1;
                    wrongYear = year + (yearChange * yearDirection);
                }

                // Format the wrong date - use the same month format as the original
                const wrongDate = `${wrongDay} ${month} ${wrongYear}`;

                // Ensure we don't add duplicates or the correct date
                if (!wrongDates.includes(wrongDate) && wrongDate !== correctDate) {
                    wrongDates.push(wrongDate);
                } else {
                    // Try again if we got a duplicate
                    i--;
                }
            }

            return wrongDates;
        }

        function loadHistoryQuestion() {
            // Save the current part title
            const partTitle = document.getElementById("hist-question").textContent;

            const event = historyEvents[currentHistQuestion];
            // Set the event as the question text, but preserve the part title in a data attribute
            document.getElementById("hist-question").textContent = event.event;
            document.getElementById("hist-question").dataset.partTitle = partTitle;

            // Generate 3 wrong dates
            const wrongDates = generateWrongDates(event.date, 3);

            // Combine correct and wrong dates
            const allDates = [event.date, ...wrongDates];

            // Shuffle the dates
            const shuffledDates = shuffleArray(allDates);

            // Find the index of the correct answer
            const correctIndex = shuffledDates.indexOf(event.date);

            // Store the correct index for checking later
            document.getElementById("hist-question-block").dataset.correctIndex = correctIndex;

            // Create the multiple choice options
            const dateInputsDiv = document.getElementById("date-inputs");
            dateInputsDiv.innerHTML = shuffledDates.map((date, index) => `
                <div class="date-option">
                    <input type="radio" name="date-choice" id="date-${index}" value="${index}" ${index === 0 ? 'checked' : ''}>
                    <label for="date-${index}" class="date-label">${date}</label>
                </div>
            `).join('');

            document.getElementById("hist-result").textContent = "";
            document.getElementById("hist-continue").style.display = "none";
            document.getElementById("hist-retry").style.display = "none";
            document.getElementById("hist-prev").style.display = "none";
            document.getElementById("hist-submit").disabled = false;
            document.getElementById("hist-skip").disabled = false;

            // Make sure the skip button is visible
            document.getElementById("hist-skip").style.display = "inline-block";
            document.getElementById("hist-submit").style.display = "inline-block";

            // Update progress indicator
            const progressText = `السؤال ${currentHistQuestion + 1} من ${historyEvents.length}`;
            document.getElementById("hist-progress").textContent = progressText;

            // Add part title to the progress indicator
            if (partTitle && partTitle !== event.event) {
                document.getElementById("hist-progress").textContent += ` - ${partTitle}`;
            }
        }

        // Event Listeners
        document.getElementById("geo-submit").addEventListener("click", function() {
            const userAnswer = document.getElementById("answer-input").value.trim();
            const correctAnswer = geoQuestions[currentGeoQuestion].definition;

            // Allow empty answers - removed validation check

            const similarity = calculateSimilarity(userAnswer, correctAnswer);
            let feedback = "";

            let scoreToAdd = 0;

            if (similarity >= 80) {
                scoreToAdd = 1;
                feedback = "✅ إجابة صحيحة";
            } else if (similarity >= 50) {
                scoreToAdd = 0.5;
                feedback = "⚠️ إجابة صحيحة جزئياً";
            } else {
                scoreToAdd = 0;
                feedback = "❌ إجابة غير صحيحة";
            }

            // Apply hint penalty if hints were used
            if (currentQuestionHintLevel > 0 && scoreToAdd > 0) {
                const totalPenalty = hintPenalty * currentQuestionHintLevel;
                scoreToAdd = Math.max(0, scoreToAdd - totalPenalty); // Ensure score doesn't go negative
                feedback += ` (تم خصم ${totalPenalty * 100}% لاستخدام التلميح)`;
            }

            // Add the score
            geoScore += scoreToAdd;

            // Show feedback and comparison (replacing the separate correct answer block)
            document.getElementById("geo-result").innerHTML = `
                <div>${feedback}</div>
                ${generateComparisonHTML(userAnswer, correctAnswer)}
            `;
            document.getElementById("geo-submit").disabled = true;
            document.getElementById("geo-skip").disabled = true;
            document.getElementById("answer-input").disabled = true;
            document.getElementById("geo-continue").style.display = "inline-block";
            document.getElementById("geo-retry").style.display = "inline-block";
        });

        document.getElementById("hist-submit").addEventListener("click", function() {
            const event = historyEvents[currentHistQuestion];

            // Get the selected option
            const selectedOption = document.querySelector('input[name="date-choice"]:checked');
            const selectedIndex = parseInt(selectedOption.value);

            // Get the correct index from the data attribute
            const correctIndex = parseInt(document.getElementById("hist-question-block").dataset.correctIndex);

            let feedback = "";
            if (selectedIndex === correctIndex) {
                feedback = "✅ إجابة صحيحة!";
                histScore++;
            } else {
                feedback = "❌ إجابة غير صحيحة";
            }

            // Show feedback and correct answer
            document.getElementById("hist-result").innerHTML = `
                <div>${feedback}</div>
                <div class="correct-answer">
                    <strong>الإجابة الصحيحة:</strong> ${event.date} - ${event.event}
                </div>
            `;

            // Highlight the correct answer and user's choice
            const dateOptions = document.querySelectorAll('.date-option');
            dateOptions.forEach((option, index) => {
                const radio = option.querySelector('input[type="radio"]');
                const label = option.querySelector('.date-label');

                if (index === correctIndex) {
                    // Correct answer
                    label.classList.add('correct-date');
                } else if (index === selectedIndex) {
                    // User's incorrect choice
                    label.classList.add('incorrect-date');
                }

                // Disable all options
                radio.disabled = true;
            });

            document.getElementById("hist-continue").style.display = "inline-block";
            document.getElementById("hist-retry").style.display = "inline-block";

            // Show previous question button if we're not on the first question
            if (currentHistQuestion > 0) {
                document.getElementById("hist-prev").style.display = "inline-block";
            }

            document.getElementById("hist-submit").disabled = true;
            document.getElementById("hist-skip").disabled = true;
        });

        // Hint button for geography
        document.getElementById("geo-hint").addEventListener("click", function() {
            // Increment hint level for this question
            currentQuestionHintLevel++;

            // Get the correct answer
            const correctAnswer = geoQuestions[currentGeoQuestion].definition;

            // Generate smart hint using our new function
            const hint = createSmartHint(correctAnswer, currentQuestionHintLevel);

            // Calculate total penalty
            const totalPenalty = hintPenalty * currentQuestionHintLevel;

            // Display the hint
            document.getElementById("geo-result").innerHTML = `
                <div class="hint-container">
                    <strong>تلميح (${currentQuestionHintLevel}):</strong> ${hint}
                    <p class="hint-penalty">سيتم خصم ${totalPenalty * 100}% من نقاطك لهذا السؤال</p>
                </div>
            `;

            // Disable the hint button if all letters are revealed
            const words = correctAnswer.split(' ');
            const totalLetters = words.reduce((sum, word) => sum + word.length, 0);
            if (currentQuestionHintLevel >= totalLetters) {
                document.getElementById("geo-hint").disabled = true;
            }
        });

        // Skip button for geography
        document.getElementById("geo-skip").addEventListener("click", function() {
            currentGeoQuestion++;
            if (currentGeoQuestion < geoQuestions.length) {
                loadGeographyQuestion();
            } else {
                document.getElementById("geo-question").textContent = `النتيجة النهائية: ${geoScore} من ${geoQuestions.length}`;
                document.getElementById("geo-answers-block").style.display = "none";
                document.getElementById("geo-submit").style.display = "none";
                document.getElementById("geo-skip").style.display = "none";
                document.getElementById("geo-hint").style.display = "none";
                document.getElementById("geo-retry").style.display = "none";
                document.getElementById("geo-continue").style.display = "none";
            }
        });

        // Retry button for geography
        document.getElementById("geo-retry").addEventListener("click", function() {
            // Reset the current question without changing the score
            document.getElementById("answer-input").value = "";
            document.getElementById("geo-result").textContent = "";
            document.getElementById("geo-submit").disabled = false;
            document.getElementById("geo-skip").disabled = false;
            document.getElementById("answer-input").disabled = false;
            document.getElementById("geo-continue").style.display = "none";
            document.getElementById("geo-retry").style.display = "none";

            // Reset the hint level for this question
            currentQuestionHintLevel = 0;

            // Re-enable the hint button
            document.getElementById("geo-hint").disabled = false;
        });

        document.getElementById("geo-continue").addEventListener("click", function() {
            currentGeoQuestion++;
            if (currentGeoQuestion < geoQuestions.length) {
                loadGeographyQuestion();
            } else {
                document.getElementById("geo-question").textContent = `النتيجة النهائية: ${geoScore} من ${geoQuestions.length}`;
                document.getElementById("geo-answers-block").style.display = "none";
                document.getElementById("geo-submit").style.display = "none";
                document.getElementById("geo-skip").style.display = "none";
                document.getElementById("geo-continue").style.display = "none";
            }
        });

        // Skip button for history
        document.getElementById("hist-skip").addEventListener("click", function() {
            currentHistQuestion++;
            if (currentHistQuestion < historyEvents.length) {
                loadHistoryQuestion();
                // Hide the previous question button until user answers
                document.getElementById("hist-prev").style.display = "none";
            } else {
                // Get the part title for the results
                const partTitle = document.getElementById("hist-question").dataset.partTitle;
                let resultTitle = `النتيجة النهائية: ${histScore} من ${historyEvents.length}`;
                if (partTitle && !partTitle.includes("كل الأجزاء")) {
                    resultTitle += ` - ${partTitle}`;
                }

                document.getElementById("hist-question").textContent = resultTitle;
                document.getElementById("hist-answers-block").style.display = "none";
                document.getElementById("hist-submit").style.display = "none";
                document.getElementById("hist-skip").style.display = "none";
                document.getElementById("hist-prev").style.display = "none";
                document.getElementById("hist-retry").style.display = "none";
                document.getElementById("hist-continue").style.display = "none";
            }
        });

        // Retry button for history
        document.getElementById("hist-retry").addEventListener("click", function() {
            // Reload the current question with new options
            loadHistoryQuestion();
            // Hide the continue button but keep retry button visible
            document.getElementById("hist-continue").style.display = "none";
            // Clear the result
            document.getElementById("hist-result").textContent = "";
        });

        // Previous question button for history
        document.getElementById("hist-prev").addEventListener("click", function() {
            if (currentHistQuestion > 0) {
                currentHistQuestion--;
                loadHistoryQuestion();
                document.getElementById("hist-prev").style.display = "none";
            }
        });

        document.getElementById("hist-continue").addEventListener("click", function() {
            currentHistQuestion++;
            if (currentHistQuestion < historyEvents.length) {
                loadHistoryQuestion();
                // Hide the previous question button until user answers
                document.getElementById("hist-prev").style.display = "none";
            } else {
                // Get the part title for the results
                const partTitle = document.getElementById("hist-question").dataset.partTitle;
                let resultTitle = `النتيجة النهائية: ${histScore} من ${historyEvents.length}`;
                if (partTitle && !partTitle.includes("كل الأجزاء")) {
                    resultTitle += ` - ${partTitle}`;
                }

                document.getElementById("hist-question").textContent = resultTitle;
                document.getElementById("hist-answers-block").style.display = "none";
                document.getElementById("hist-submit").style.display = "none";
                document.getElementById("hist-skip").style.display = "none";
                document.getElementById("hist-prev").style.display = "none";
                document.getElementById("hist-retry").style.display = "none";
                document.getElementById("hist-continue").style.display = "none";
            }
        });

        document.getElementById("geo-reset").addEventListener("click", function() {
            // Reshuffle questions when resetting
            geoQuestions = shuffleArray(geoQuestionsOriginal);
            currentGeoQuestion = 0;
            geoScore = 0;
            document.getElementById("geo-answers-block").style.display = "block";
            document.getElementById("geo-submit").style.display = "inline-block";
            document.getElementById("geo-skip").style.display = "inline-block";
            document.getElementById("geo-hint").style.display = "inline-block";
            loadGeographyQuestion();
        });

        // Philosophy Quiz Functions
        function loadPhilosophyPart(part) {
            // Reset score and current question
            philScore = 0;
            currentPhilQuestion = 0;

            // Load the appropriate part
            switch(part) {
                case 1:
                    philosophyQuestions = shuffleArray(philosophyDeterministicPart);
                    document.getElementById("phil-question").textContent = "الحجج الحتمية";
                    break;
                case 2:
                    philosophyQuestions = shuffleArray(philosophyIndeterministicPart);
                    document.getElementById("phil-question").textContent = "الحجج اللاحتمية";
                    break;
                default:
                    // Load all parts
                    philosophyQuestions = shuffleArray(philosophyQuestionsOriginal);
                    document.getElementById("phil-question").textContent = "الحتمية/اللاحتمية";
                    break;
            }
        }

        function loadPhilosophyQuestion() {
            // Save the current part title
            const partTitle = document.getElementById("phil-question").textContent;

            // Get the current question
            const question = philosophyQuestions[currentPhilQuestion];

            // Set the question text, but preserve the part title in a data attribute
            document.getElementById("phil-question").textContent = question.question;
            document.getElementById("phil-question").dataset.partTitle = partTitle;

            // Reset UI elements
            document.getElementById("phil-answer-input").value = "";
            document.getElementById("phil-result").textContent = "";
            document.getElementById("phil-continue").style.display = "none";
            document.getElementById("phil-retry").style.display = "none";
            document.getElementById("phil-prev").style.display = "none";
            document.getElementById("phil-submit").disabled = false;
            document.getElementById("phil-skip").disabled = false;
            document.getElementById("phil-hint").disabled = false;
            document.getElementById("phil-learn").disabled = false;
            document.getElementById("phil-answer-input").disabled = false;

            // Reset hint level for the new question
            philHintLevel = 0;

            // Make sure all relevant buttons are visible
            document.getElementById("phil-skip").style.display = "inline-block";
            document.getElementById("phil-submit").style.display = "inline-block";
            document.getElementById("phil-hint").style.display = "inline-block";
            document.getElementById("phil-learn").style.display = "inline-block";

            // Update progress indicator
            const progressText = `السؤال ${currentPhilQuestion + 1} من ${philosophyQuestions.length}`;
            document.getElementById("phil-progress").textContent = progressText;

            // Add part title to the progress indicator
            if (partTitle && partTitle !== question.question) {
                document.getElementById("phil-progress").textContent += ` - ${partTitle}`;
            }
        }

        // Philosophy Quiz Event Listeners
        document.getElementById("phil-submit").addEventListener("click", function() {
            const userAnswer = document.getElementById("phil-answer-input").value.trim();
            const correctAnswer = philosophyQuestions[currentPhilQuestion].answer;

            // Calculate similarity between user answer and correct answer
            const similarity = calculateSimilarity(userAnswer, correctAnswer);
            let feedback = "";
            let scoreToAdd = 0;

            if (similarity >= 80) {
                scoreToAdd = 1;
                feedback = "✅ إجابة صحيحة";
            } else if (similarity >= 50) {
                scoreToAdd = 0.5;
                feedback = "⚠️ إجابة صحيحة جزئياً";
            } else {
                scoreToAdd = 0;
                feedback = "❌ إجابة غير صحيحة";
            }

            // Apply hint penalty if hints were used
            if (philHintLevel > 0 && scoreToAdd > 0) {
                const totalPenalty = hintPenalty * philHintLevel;
                scoreToAdd = Math.max(0, scoreToAdd - totalPenalty); // Ensure score doesn't go negative
                feedback += ` (تم خصم ${totalPenalty * 100}% لاستخدام التلميح)`;
            }

            // Add the score
            philScore += scoreToAdd;

            // Show feedback and comparison
            document.getElementById("phil-result").innerHTML = `
                <div>${feedback}</div>
                ${generateComparisonHTML(userAnswer, correctAnswer)}
            `;

            // Disable input and buttons
            document.getElementById("phil-submit").disabled = true;
            document.getElementById("phil-skip").disabled = true;
            document.getElementById("phil-hint").disabled = true;
            document.getElementById("phil-learn").disabled = true;
            document.getElementById("phil-answer-input").disabled = true;

            // Show continue and retry buttons
            document.getElementById("phil-continue").style.display = "inline-block";
            document.getElementById("phil-retry").style.display = "inline-block";

            // Show previous question button if we're not on the first question
            if (currentPhilQuestion > 0) {
                document.getElementById("phil-prev").style.display = "inline-block";
            }
        });

        document.getElementById("phil-skip").addEventListener("click", function() {
            currentPhilQuestion++;
            if (currentPhilQuestion < philosophyQuestions.length) {
                loadPhilosophyQuestion();
            } else {
                // Get the part title for the results
                const partTitle = document.getElementById("phil-question").dataset.partTitle;
                let resultTitle = `النتيجة النهائية: ${philScore} من ${philosophyQuestions.length}`;
                if (partTitle && !partTitle.includes("الحتمية/اللاحتمية")) {
                    resultTitle += ` - ${partTitle}`;
                }

                document.getElementById("phil-question").textContent = resultTitle;
                document.getElementById("phil-answers-block").style.display = "none";
                document.getElementById("phil-submit").style.display = "none";
                document.getElementById("phil-skip").style.display = "none";
                document.getElementById("phil-prev").style.display = "none";
                document.getElementById("phil-retry").style.display = "none";
                document.getElementById("phil-hint").style.display = "none";
                document.getElementById("phil-learn").style.display = "none";
                document.getElementById("phil-continue").style.display = "none";
            }
        });

        document.getElementById("phil-retry").addEventListener("click", function() {
            // Reload the current question
            loadPhilosophyQuestion();
        });

        document.getElementById("phil-prev").addEventListener("click", function() {
            if (currentPhilQuestion > 0) {
                currentPhilQuestion--;
                loadPhilosophyQuestion();
            }
        });

        document.getElementById("phil-continue").addEventListener("click", function() {
            currentPhilQuestion++;
            if (currentPhilQuestion < philosophyQuestions.length) {
                loadPhilosophyQuestion();
            } else {
                // Get the part title for the results
                const partTitle = document.getElementById("phil-question").dataset.partTitle;
                let resultTitle = `النتيجة النهائية: ${philScore} من ${philosophyQuestions.length}`;
                if (partTitle && !partTitle.includes("الحتمية/اللاحتمية")) {
                    resultTitle += ` - ${partTitle}`;
                }

                document.getElementById("phil-question").textContent = resultTitle;
                document.getElementById("phil-answers-block").style.display = "none";
                document.getElementById("phil-submit").style.display = "none";
                document.getElementById("phil-skip").style.display = "none";
                document.getElementById("phil-prev").style.display = "none";
                document.getElementById("phil-retry").style.display = "none";
                document.getElementById("phil-hint").style.display = "none";
                document.getElementById("phil-learn").style.display = "none";
                document.getElementById("phil-continue").style.display = "none";
            }
        });

        document.getElementById("phil-hint").addEventListener("click", function() {
            // Increment hint level for this question
            philHintLevel++;

            // Get the correct answer
            const correctAnswer = philosophyQuestions[currentPhilQuestion].answer;

            // Generate smart hint using our new function
            const hint = createSmartHint(correctAnswer, philHintLevel);

            // Calculate total penalty
            const totalPenalty = hintPenalty * philHintLevel;

            // Display the hint
            document.getElementById("phil-result").innerHTML = `
                <div class="hint-container">
                    <strong>تلميح (${philHintLevel}):</strong> ${hint}
                    <p class="hint-penalty">سيتم خصم ${totalPenalty * 100}% من نقاطك لهذا السؤال</p>
                </div>
            `;

            // Disable the hint button if all letters are revealed
            const words = correctAnswer.split(' ');
            const totalLetters = words.reduce((sum, word) => sum + word.length, 0);
            if (philHintLevel >= totalLetters) {
                document.getElementById("phil-hint").disabled = true;
            }
        });

        document.getElementById("phil-learn").addEventListener("click", function() {
            const correctAnswer = philosophyQuestions[currentPhilQuestion].answer;

            // Display the answer with an explanation
            document.getElementById("phil-result").innerHTML = `
                <div class="learn-container">
                    <strong>الإجابة:</strong> ${correctAnswer}
                    <p class="learn-note">ملاحظة: استخدام زر "عرض الإجابة/تعلم" لا يضيف نقاطاً.</p>
                </div>
            `;

            // Show the continue button to move to the next question
            document.getElementById("phil-continue").style.display = "inline-block";

            // Disable the submit button since the answer is shown
            document.getElementById("phil-submit").disabled = true;
            document.getElementById("phil-hint").disabled = true;
            document.getElementById("phil-learn").disabled = true;
        });

        document.getElementById("phil-reset").addEventListener("click", function() {
            // Get the current part title from the data attribute
            const partTitle = document.getElementById("phil-question").dataset.partTitle;

            // Determine which part to reload based on the title
            let part = 0; // Default to all parts
            if (partTitle === "الحجج الحتمية") part = 1;
            else if (partTitle === "الحجج اللاحتمية") part = 2;

            // Load the appropriate part
            loadPhilosophyPart(part);

            // Reset UI elements
            document.getElementById("phil-answers-block").style.display = "block";
            document.getElementById("phil-submit").style.display = "inline-block";
            document.getElementById("phil-skip").style.display = "inline-block";
            document.getElementById("phil-hint").style.display = "inline-block";
            document.getElementById("phil-learn").style.display = "inline-block";

            loadPhilosophyQuestion();
        });

        document.getElementById("hist-reset").addEventListener("click", function() {
            // Get the current part title from the data attribute
            const partTitle = document.getElementById("hist-question").dataset.partTitle;

            // Determine which part to reload based on the title
            let part = 0; // Default to all parts
            if (partTitle === "الجزء 1") part = 1;
            else if (partTitle === "الجزء 2") part = 2;
            else if (partTitle === "الجزء 3") part = 3;
            else if (partTitle === "الجزء 4") part = 4;
            else if (partTitle === "الجزء 5") part = 5;
            else if (partTitle === "الجزء 6") part = 6;
            else if (partTitle === "الجزء 7") part = 7;
            else if (partTitle === "الجزء 8") part = 8;
            else if (partTitle === "الجزء 9") part = 9;
            else if (partTitle === "الجزء 10") part = 10;

            // Load the appropriate part
            loadHistoryPart(part);

            // Reset UI elements
            currentHistQuestion = 0;
            histScore = 0;
            document.getElementById("hist-answers-block").style.display = "block";
            document.getElementById("hist-submit").style.display = "inline-block";
            document.getElementById("hist-skip").style.display = "inline-block";
            loadHistoryQuestion();
        });

        // Islamic Law Event Listeners
        document.getElementById("islamic-submit").addEventListener("click", function() {
            checkIslamicLawAnswer();
        });

        document.getElementById("islamic-continue").addEventListener("click", function() {
            currentIslamicQuestion++;
            if (currentIslamicQuestion < islamicLawQuestions.length) {
                loadIslamicLawQuestion();
            } else {
                // End of quiz
                document.getElementById("islamic-question").textContent = "انتهى الاختبار!";
                document.getElementById("islamic-answers-block").style.display = "none";
                document.getElementById("islamic-submit").style.display = "none";
                document.getElementById("islamic-continue").style.display = "none";
                document.getElementById("islamic-skip").style.display = "none";
                document.getElementById("islamic-hint").style.display = "none";

                const percentage = Math.round((islamicScore / islamicLawQuestions.length) * 100);
                document.getElementById("islamic-result").innerHTML = `
                    <div class="final-score">
                        <h3>النتيجة النهائية:</h3>
                        <p>${islamicScore} من ${islamicLawQuestions.length} (${percentage}%)</p>
                    </div>
                `;
            }
        });

        document.getElementById("islamic-skip").addEventListener("click", function() {
            // Show the correct answer
            const question = islamicLawQuestions[currentIslamicQuestion];
            const answersHTML = question.answers.map(answer => `<li>${answer}</li>`).join('');

            document.getElementById("islamic-result").innerHTML = `
                <div class="correct-answer">
                    <h3>الإجابة الصحيحة:</h3>
                    <ul>${answersHTML}</ul>
                </div>
            `;

            // Disable input and buttons
            document.getElementById("islamic-answer-input").disabled = true;
            document.getElementById("islamic-submit").style.display = "none";
            document.getElementById("islamic-skip").disabled = true;
            document.getElementById("islamic-hint").disabled = true;

            // Show continue button
            document.getElementById("islamic-continue").style.display = "inline-block";
        });

        document.getElementById("islamic-reset").addEventListener("click", function() {
            // Reset the quiz
            islamicLawQuestions = [...islamicLawQuestionsOriginal];
            currentIslamicQuestion = 0;
            islamicScore = 0;
            loadIslamicLawQuestion();
        });

        document.getElementById("islamic-hint").addEventListener("click", function() {
            // Increment hint level for this question
            islamicHintLevel++;

            // Get the correct answers
            const question = islamicLawQuestions[currentIslamicQuestion];
            const answers = question.answers;

            // Generate hints for each answer
            const hints = answers.map(answer => createSmartHint(answer, islamicHintLevel));

            // Calculate total penalty
            const totalPenalty = hintPenalty * islamicHintLevel;

            // Display the hints
            const hintsHTML = hints.map((hint, index) =>
                `<div class="hint-item"><strong>تلميح ${index + 1}:</strong> ${hint}</div>`
            ).join('');

            document.getElementById("islamic-result").innerHTML = `
                <div class="hint-container">
                    ${hintsHTML}
                    <p class="hint-penalty">سيتم خصم ${totalPenalty * 100}% من نقاطك لهذا السؤال</p>
                </div>
            `;

            // Disable the hint button if we've revealed a significant number of letters
            const maxHintLevel = 20; // Cap at 20 to prevent excessive hints
            if (islamicHintLevel >= maxHintLevel) {
                document.getElementById("islamic-hint").disabled = true;
            }
        });

        document.getElementById("islamic-learn").addEventListener("click", function() {
            // Show the correct answer for learning purposes
            const question = islamicLawQuestions[currentIslamicQuestion];
            const answersHTML = question.answers.map(answer => `<li>${answer}</li>`).join('');

            document.getElementById("islamic-result").innerHTML = `
                <div class="learn-container">
                    <h3>الإجابة الصحيحة:</h3>
                    <ul>${answersHTML}</ul>
                    <p class="learn-note">ملاحظة: استخدام زر "تعلم" لا يؤثر على نقاطك.</p>
                </div>
            `;
        });

        // Utility Functions

        // Normalize Arabic text (remove diacritics, standardize characters)
        function normalizeArabic(text) {
            return text
                .replace(/[\u064B-\u065F]/g, '') // Remove tashkeel (diacritics)
                .replace(/[\u0629]/g, '\u0647') // Convert taa marbouta to haa
                .replace(/[\u0623\u0625\u0622\u0627\u0671]/g, '\u0627') // Normalize alef variations
                .replace(/[\u0621\u0624\u0626]/g, '') // Remove hamza
                .replace(/\s+/g, ' ') // Normalize spaces
                .replace(/[\.,\u060c:;\u061b]/g, '') // Remove punctuation
                .replace(/\u0627\u0644/g, ''); // Remove 'al' prefix
        }

        function calculateSimilarity(userAnswer, correctAnswer) {
            // Original similarity calculation logic with enhancements for color coding

            // Prepare the answers
            const normalizedUserAnswer = normalizeArabic(userAnswer);
            const normalizedCorrectAnswer = normalizeArabic(correctAnswer);

            // Split into words and filter out very short words
            const userWords = normalizedUserAnswer.split(' ').filter(word => word.length > 1);
            const correctWords = normalizedCorrectAnswer.split(' ').filter(word => word.length > 1);

            // Initialize counters
            let matchedWords = 0;
            let partialMatches = 0;
            let totalPenalty = 0;

            // Track which words are matched for highlighting
            const wordStatus = {};
            correctWords.forEach(word => {
                wordStatus[word] = 'missing';
            });

            // Create normalized versions of all words for comparison
            const normalizedUserWordsMap = {};
            userWords.forEach(word => {
                normalizedUserWordsMap[word] = normalizeArabic(word);
            });

            const normalizedCorrectWordsMap = {};
            correctWords.forEach(word => {
                normalizedCorrectWordsMap[word] = normalizeArabic(word);
            });

            // First pass: find exact matches using normalized comparison
            correctWords.forEach(correctWord => {
                const normalizedCorrectWord = normalizedCorrectWordsMap[correctWord];
                // Check for exact word matches with normalization
                for (const userWord of userWords) {
                    if (normalizedUserWordsMap[userWord] === normalizedCorrectWord) {
                        matchedWords++;
                        wordStatus[correctWord] = 'correct';
                        break;
                    }
                }
            });

            // Second pass: find partial matches for remaining words
            correctWords.forEach(correctWord => {
                if (wordStatus[correctWord] === 'missing') {
                    const normalizedCorrectWord = normalizedCorrectWordsMap[correctWord];
                    // Check for partial matches with normalization
                    for (const userWord of userWords) {
                        const normalizedUserWord = normalizedUserWordsMap[userWord];
                        if (normalizedUserWord.length > 2 && normalizedCorrectWord.length > 2 &&
                            (normalizedCorrectWord.includes(normalizedUserWord) || normalizedUserWord.includes(normalizedCorrectWord))) {
                            partialMatches += 0.5;
                            wordStatus[correctWord] = 'partial';
                            break;
                        }
                    }
                }
            });

            // Calculate penalties for format, punctuation, etc.
            // These don't count as incorrect answers but reduce the score slightly

            // Check for punctuation differences
            const correctPunctuation = (correctAnswer.match(/[\.,،:;؛]/g) || []).length;
            const userPunctuation = (userAnswer.match(/[\.,،:;؛]/g) || []).length;
            if (Math.abs(correctPunctuation - userPunctuation) > 0) {
                totalPenalty += 0.05; // 5% penalty for punctuation differences
            }

            // Check for 'al' (ال) prefix differences
            const correctAl = (correctAnswer.match(/ال/g) || []).length;
            const userAl = (userAnswer.match(/ال/g) || []).length;
            if (Math.abs(correctAl - userAl) > 0) {
                totalPenalty += 0.05; // 5% penalty for 'al' differences
            }

            // Check for hamza differences
            const correctHamza = (correctAnswer.match(/[ءأإؤئآ]/g) || []).length;
            const userHamza = (userAnswer.match(/[ءأإؤئآ]/g) || []).length;
            if (Math.abs(correctHamza - userHamza) > 0) {
                totalPenalty += 0.05; // 5% penalty for hamza differences
            }

            // Calculate final similarity score
            const rawSimilarity = ((matchedWords + partialMatches) / correctWords.length);
            const finalSimilarity = Math.max(0, (rawSimilarity - totalPenalty)) * 100;

            // Store the word status and similarity for highlighting
            window.lastComparisonResult = {
                userWords: userWords,
                correctWords: correctWords,
                wordStatus: wordStatus,
                similarity: finalSimilarity,
                rawSimilarity: rawSimilarity * 100,
                penalties: totalPenalty * 100
            };

            // For debugging
            console.log('Comparison result:', window.lastComparisonResult);

            return finalSimilarity;
        }

        // Smart hint function that prioritizes longer words and reveals letters strategically
        function createSmartHint(correctAnswer, hintLevel) {
            // If hint level is 0 or negative, return all dots
            if (hintLevel <= 0) {
                return correctAnswer.split('').map(c => c === ' ' ? ' ' : '.').join('');
            }

            // Split the answer into words
            const words = correctAnswer.split(' ');

            // Create a map to track how many letters have been revealed for each word
            const revealedLetters = words.map(() => 0);

            // Calculate total letters to reveal based on hint level
            let lettersToReveal = Math.min(hintLevel,
                                          words.reduce((sum, word) => sum + word.length, 0));

            // Create a list of words with their indices, sorted by length (longest first)
            const wordsByLength = words.map((word, index) => ({
                word,
                index,
                length: word.length
            })).sort((a, b) => b.length - a.length);

            // Distribute reveals among words, prioritizing longer words
            // Continue until we've used all reveals or revealed all letters
            while (lettersToReveal > 0) {
                // Find words that still have unrevealed letters
                const eligibleWords = wordsByLength.filter(({ word, index }) =>
                    revealedLetters[index] < word.length);

                // If no eligible words, break
                if (eligibleWords.length === 0) break;

                // Reveal one letter in each eligible word, starting with longest
                for (const { index } of eligibleWords) {
                    if (lettersToReveal <= 0) break;
                    revealedLetters[index]++;
                    lettersToReveal--;
                }
            }

            // Build the hint string
            let hint = '';
            for (let i = 0; i < words.length; i++) {
                const word = words[i];
                if (word.length > 0) {
                    // Add revealed letters
                    hint += word.substring(0, revealedLetters[i]);

                    // Add dots for remaining letters
                    for (let j = revealedLetters[i]; j < word.length; j++) {
                        hint += '.';
                    }
                    hint += ' ';
                }
            }

            return hint.trim();
        }

        // Function to load Islamic Law questions
        function loadIslamicLawQuestion() {
            const question = islamicLawQuestions[currentIslamicQuestion];
            document.getElementById("islamic-question").textContent = question.question;
            document.getElementById("islamic-answer-input").value = "";
            document.getElementById("islamic-result").innerHTML = "";
            document.getElementById("islamic-continue").style.display = "none";
            document.getElementById("islamic-retry").style.display = "none";
            document.getElementById("islamic-submit").style.display = "inline-block";
            document.getElementById("islamic-submit").disabled = false;
            document.getElementById("islamic-skip").disabled = false;
            document.getElementById("islamic-hint").disabled = false;
            document.getElementById("islamic-answer-input").disabled = false;

            // Reset hint usage for the new question
            islamicHintLevel = 0;

            // Make sure all relevant buttons are visible
            document.getElementById("islamic-skip").style.display = "inline-block";
            document.getElementById("islamic-hint").style.display = "inline-block";

            // Update progress indicator
            const progressText = `السؤال ${currentIslamicQuestion + 1} من ${islamicLawQuestions.length}`;
            document.getElementById("islamic-progress").textContent = progressText;
        }

        function generateComparisonHTML(userAnswer, correctAnswer) {
            try {
                const result = window.lastComparisonResult;
                if (!result) {
                    console.error('No comparison result available');
                    return '<div class="comparison-container">Error generating comparison</div>';
                }

                // Access the normalizeArabic function from the outer scope

                // Create a more detailed comparison with percentage display
                const userWords = userAnswer.split(' ');
                const correctWords = correctAnswer.split(' ');

                // Color code the user's answer
                const coloredUserWords = userWords.map(userWord => {
                    // Get the normalized version for comparison
                    const normalizedUserWord = normalizeArabic(userWord);

                    // Check if this exact word is in the correct answer
                    if (correctWords.some(correctWord => normalizeArabic(correctWord) === normalizedUserWord)) {
                        return `<span class="correct-word">${userWord}</span>`;
                    }
                    // Check for partial matches
                    else if (correctWords.some(correctWord => {
                        const normalizedCorrectWord = normalizeArabic(correctWord);
                        return normalizedUserWord.length > 2 && normalizedCorrectWord.length > 2 &&
                            (normalizedCorrectWord.includes(normalizedUserWord) || normalizedUserWord.includes(normalizedCorrectWord));
                    })) {
                        return `<span class="partial-word">${userWord}</span>`;
                    }
                    // Otherwise it's incorrect
                    else {
                        return `<span class="incorrect-word">${userWord}</span>`;
                    }
                });

                // Color code the correct answer
                const coloredCorrectWords = correctWords.map(correctWord => {
                    // Get the normalized version for comparison
                    const normalizedCorrectWord = normalizeArabic(correctWord);

                    // Check if this word is in the user's answer
                    if (userWords.some(userWord => normalizeArabic(userWord) === normalizedCorrectWord)) {
                        return `<span class="correct-word">${correctWord}</span>`;
                    }
                    // Check for partial matches
                    else if (userWords.some(userWord => {
                        const normalizedUserWord = normalizeArabic(userWord);
                        return normalizedUserWord.length > 2 && normalizedCorrectWord.length > 2 &&
                            (normalizedCorrectWord.includes(normalizedUserWord) || normalizedUserWord.includes(normalizedCorrectWord));
                    })) {
                        return `<span class="partial-word">${correctWord}</span>`;
                    }
                    // Otherwise it's missing
                    else {
                        return `<span class="missing-word">${correctWord}</span>`;
                    }
                });

                // Format the similarity percentage
                const similarityPercentage = result.similarity.toFixed(1);
                const rawSimilarity = result.rawSimilarity ? result.rawSimilarity.toFixed(1) : "N/A";
                const penalties = result.penalties ? result.penalties.toFixed(1) : "0.0";

                return `
                    <div class="comparison-container">
                        <div class="comparison-title">مقارنة الإجابات:</div>
                        <div class="similarity-score">
                            <span>نسبة التطابق: <strong>${similarityPercentage}%</strong></span>
                            <span class="score-details">(التطابق الأساسي: ${rawSimilarity}% | الخصومات: ${penalties}%)</span>
                        </div>
                        <div class="user-answer-comparison">
                            <strong>إجابتك:</strong> ${coloredUserWords.join(' ')}
                        </div>
                        <div class="correct-answer-comparison">
                            <strong>الإجابة الصحيحة:</strong> ${coloredCorrectWords.join(' ')}
                        </div>
                    </div>
                `;
            } catch (error) {
                console.error('Error generating comparison:', error);
                return '';
            }
        }

        // Crossword Game Logic
        let currentCrosswordQuestion = 0;
        let crosswordScore = 0;
        let currentCrosswordGrid = [];
        let currentCrosswordAnswer = "";
        let selectedCells = [];

        // Create a comprehensive crossword puzzle from all geography questions
        function createComprehensiveCrossword() {
            // Collect main words from all questions
            const mainWords = [];
            const questionMap = {}; // Maps words to their questions

            geoQuestionsOriginal.forEach((question, index) => {
                const answer = question.definition;

                // Remove punctuation and normalize spaces
                const cleanAnswer = answer.replace(/[.,\/#!$%\^&\*;:{}=\-_`~()]/g, "").replace(/\s+/g, " ").trim();

                // Get words from the answer
                const words = cleanAnswer.split(' ').filter(word => word.length > 0);

                // Make sure we have at least one word
                if (words.length === 0) {
                    return; // Skip this question if no valid words
                }

                // Use the longest word as the main word for this question
                let mainWordIndex = 0;
                let maxLength = words[0].length;

                for (let i = 1; i < words.length; i++) {
                    if (words[i].length > maxLength) {
                        maxLength = words[i].length;
                        mainWordIndex = i;
                    }
                }

                const mainWord = words[mainWordIndex];

                // Only use words that are at least 3 characters long
                if (mainWord.length >= 3) {
                    mainWords.push({
                        word: mainWord,
                        questionIndex: index,
                        length: mainWord.length
                    });

                    // Store the question for this word
                    questionMap[mainWord] = question;
                }
            });

            // Sort words by length (longest first) to prioritize placement
            mainWords.sort((a, b) => b.length - a.length);

            // Create a large grid for the comprehensive crossword
            const gridSize = 25; // Adjust based on the number of questions
            const grid = [];
            for (let i = 0; i < gridSize; i++) {
                const row = [];
                for (let j = 0; j < gridSize; j++) {
                    row.push({ letter: '', isActive: false, isCorrect: false });
                }
                grid.push(row);
            }

            // Place words on the grid
            const placedWords = [];
            const clues = [];
            let clueNumber = 1;

            // Place the first word horizontally in the middle
            if (mainWords.length > 0) {
                const firstWord = mainWords[0].word;
                const firstWordRow = Math.floor(gridSize / 2);
                const firstWordStartCol = Math.floor((gridSize - firstWord.length) / 2);

                // Place the word
                for (let i = 0; i < firstWord.length; i++) {
                    grid[firstWordRow][firstWordStartCol + i].isActive = true;
                    grid[firstWordRow][firstWordStartCol + i].correctLetter = firstWord[i];
                    grid[firstWordRow][firstWordStartCol + i].wordIndex = 0;
                    grid[firstWordRow][firstWordStartCol + i].position = i;
                    grid[firstWordRow][firstWordStartCol + i].clueNumber = clueNumber;
                    grid[firstWordRow][firstWordStartCol + i].direction = 'horizontal';
                    grid[firstWordRow][firstWordStartCol + i].questionIndex = mainWords[0].questionIndex;
                }

                // Add clue
                clues.push({
                    number: clueNumber,
                    direction: 'أفقي',
                    word: firstWord,
                    text: `${questionMap[firstWord].question} (${firstWord.length} أحرف)`,
                    questionIndex: mainWords[0].questionIndex
                });

                placedWords.push({
                    word: firstWord,
                    row: firstWordRow,
                    col: firstWordStartCol,
                    direction: 'horizontal',
                    clueNumber: clueNumber,
                    questionIndex: mainWords[0].questionIndex
                });

                clueNumber++;
            }

            // Try to place remaining words
            for (let i = 1; i < mainWords.length; i++) {
                const wordObj = mainWords[i];
                const word = wordObj.word;
                let placed = false;

                // Try to find intersections with already placed words
                for (let j = 0; j < placedWords.length && !placed; j++) {
                    const placedWord = placedWords[j];

                    // Try each letter in the current word
                    for (let k = 0; k < word.length && !placed; k++) {
                        const letter = word[k];

                        // Try to find this letter in the placed word
                        const placedWordText = placedWord.word;
                        for (let l = 0; l < placedWordText.length && !placed; l++) {
                            if (placedWordText[l] === letter) {
                                // Found a potential intersection
                                let newRow, newCol, canPlace = true;

                                if (placedWord.direction === 'horizontal') {
                                    // Try to place vertically
                                    newRow = placedWord.row - k;
                                    newCol = placedWord.col + l;

                                    // Check if the word fits on the grid
                                    if (newRow < 0 || newRow + word.length > gridSize) {
                                        canPlace = false;
                                        continue;
                                    }

                                    // Check if the space is available
                                    for (let m = 0; m < word.length; m++) {
                                        if (m === k) continue; // Skip the intersection point

                                        if (newRow + m < 0 || newRow + m >= gridSize ||
                                            newCol < 0 || newCol >= gridSize) {
                                            canPlace = false;
                                            break;
                                        }

                                        // Check if the cell is already occupied
                                        if (grid[newRow + m][newCol].isActive) {
                                            // If occupied, check if it's the same letter
                                            if (grid[newRow + m][newCol].correctLetter !== word[m]) {
                                                canPlace = false;
                                                break;
                                            }
                                        }

                                        // Check adjacent cells (no touching words)
                                        if (m !== k && newCol > 0 && grid[newRow + m][newCol - 1].isActive) {
                                            canPlace = false;
                                            break;
                                        }
                                        if (m !== k && newCol < gridSize - 1 && grid[newRow + m][newCol + 1].isActive) {
                                            canPlace = false;
                                            break;
                                        }
                                    }

                                    // Check cells above and below the word
                                    if (canPlace && newRow > 0 && grid[newRow - 1][newCol].isActive) {
                                        canPlace = false;
                                    }
                                    if (canPlace && newRow + word.length < gridSize &&
                                        grid[newRow + word.length][newCol].isActive) {
                                        canPlace = false;
                                    }

                                    // If we can place the word, do it
                                    if (canPlace) {
                                        for (let m = 0; m < word.length; m++) {
                                            grid[newRow + m][newCol].isActive = true;
                                            grid[newRow + m][newCol].correctLetter = word[m];
                                            grid[newRow + m][newCol].wordIndex = i;
                                            grid[newRow + m][newCol].position = m;
                                            grid[newRow + m][newCol].questionIndex = wordObj.questionIndex;

                                            // Mark the start of the word with the clue number
                                            if (m === 0) {
                                                grid[newRow][newCol].clueNumber = clueNumber;
                                                grid[newRow][newCol].direction = 'vertical';
                                            }
                                        }

                                        // Add clue
                                        clues.push({
                                            number: clueNumber,
                                            direction: 'عمودي',
                                            word: word,
                                            text: `${questionMap[word].question} (${word.length} أحرف)`,
                                            questionIndex: wordObj.questionIndex
                                        });

                                        placedWords.push({
                                            word: word,
                                            row: newRow,
                                            col: newCol,
                                            direction: 'vertical',
                                            clueNumber: clueNumber,
                                            questionIndex: wordObj.questionIndex
                                        });

                                        clueNumber++;
                                        placed = true;
                                    }
                                } else {
                                    // Try to place horizontally
                                    newRow = placedWord.row + l;
                                    newCol = placedWord.col - k;

                                    // Check if the word fits on the grid
                                    if (newCol < 0 || newCol + word.length > gridSize) {
                                        canPlace = false;
                                        continue;
                                    }

                                    // Check if the space is available
                                    for (let m = 0; m < word.length; m++) {
                                        if (m === k) continue; // Skip the intersection point

                                        if (newRow < 0 || newRow >= gridSize ||
                                            newCol + m < 0 || newCol + m >= gridSize) {
                                            canPlace = false;
                                            break;
                                        }

                                        // Check if the cell is already occupied
                                        if (grid[newRow][newCol + m].isActive) {
                                            // If occupied, check if it's the same letter
                                            if (grid[newRow][newCol + m].correctLetter !== word[m]) {
                                                canPlace = false;
                                                break;
                                            }
                                        }

                                        // Check adjacent cells (no touching words)
                                        if (m !== k && newRow > 0 && grid[newRow - 1][newCol + m].isActive) {
                                            canPlace = false;
                                            break;
                                        }
                                        if (m !== k && newRow < gridSize - 1 && grid[newRow + 1][newCol + m].isActive) {
                                            canPlace = false;
                                            break;
                                        }
                                    }

                                    // Check cells to the left and right of the word
                                    if (canPlace && newCol > 0 && grid[newRow][newCol - 1].isActive) {
                                        canPlace = false;
                                    }
                                    if (canPlace && newCol + word.length < gridSize &&
                                        grid[newRow][newCol + word.length].isActive) {
                                        canPlace = false;
                                    }

                                    // If we can place the word, do it
                                    if (canPlace) {
                                        for (let m = 0; m < word.length; m++) {
                                            grid[newRow][newCol + m].isActive = true;
                                            grid[newRow][newCol + m].correctLetter = word[m];
                                            grid[newRow][newCol + m].wordIndex = i;
                                            grid[newRow][newCol + m].position = m;
                                            grid[newRow][newCol + m].questionIndex = wordObj.questionIndex;

                                            // Mark the start of the word with the clue number
                                            if (m === 0) {
                                                grid[newRow][newCol].clueNumber = clueNumber;
                                                grid[newRow][newCol].direction = 'horizontal';
                                            }
                                        }

                                        // Add clue
                                        clues.push({
                                            number: clueNumber,
                                            direction: 'أفقي',
                                            word: word,
                                            text: `${questionMap[word].question} (${word.length} أحرف)`,
                                            questionIndex: wordObj.questionIndex
                                        });

                                        placedWords.push({
                                            word: word,
                                            row: newRow,
                                            col: newCol,
                                            direction: 'horizontal',
                                            clueNumber: clueNumber,
                                            questionIndex: wordObj.questionIndex
                                        });

                                        clueNumber++;
                                        placed = true;
                                    }
                                }
                            }
                        }
                    }
                }
            }

            // Sort clues by number
            clues.sort((a, b) => a.number - b.number);

            // Trim the grid to remove empty rows and columns
            let minRow = gridSize, maxRow = 0, minCol = gridSize, maxCol = 0;

            for (let i = 0; i < gridSize; i++) {
                for (let j = 0; j < gridSize; j++) {
                    if (grid[i][j].isActive) {
                        minRow = Math.min(minRow, i);
                        maxRow = Math.max(maxRow, i);
                        minCol = Math.min(minCol, j);
                        maxCol = Math.max(maxCol, j);
                    }
                }
            }

            // Add padding
            minRow = Math.max(0, minRow - 1);
            minCol = Math.max(0, minCol - 1);
            maxRow = Math.min(gridSize - 1, maxRow + 1);
            maxCol = Math.min(gridSize - 1, maxCol + 1);

            // Create the trimmed grid
            const trimmedGrid = [];
            for (let i = minRow; i <= maxRow; i++) {
                const row = [];
                for (let j = minCol; j <= maxCol; j++) {
                    row.push(grid[i][j]);
                }
                trimmedGrid.push(row);
            }

            // Adjust the placed words' coordinates
            placedWords.forEach(word => {
                word.row -= minRow;
                word.col -= minCol;
            });

            return { grid: trimmedGrid, clues, placedWords };
        }

        // Define question images based on topics
        const questionImages = {
            // Geography images
            'الخليج العربي': 'https://upload.wikimedia.org/wikipedia/commons/thumb/c/cd/Persian_Gulf_satellite_image.jpg/320px-Persian_Gulf_satellite_image.jpg',
            'البحر الأحمر': 'https://upload.wikimedia.org/wikipedia/commons/thumb/1/14/Red_Sea_topographic_map-en.jpg/320px-Red_Sea_topographic_map-en.jpg',
            'البحر الأبيض المتوسط': 'https://upload.wikimedia.org/wikipedia/commons/thumb/0/05/Mediterranean_Sea_political_map-en.svg/320px-Mediterranean_Sea_political_map-en.svg.png',
            'المحيط الهندي': 'https://upload.wikimedia.org/wikipedia/commons/thumb/d/d9/Indian_Ocean-CIA_WFB_Map.png/320px-Indian_Ocean-CIA_WFB_Map.png',
            'المحيط الهادئ': 'https://upload.wikimedia.org/wikipedia/commons/thumb/c/c6/Pacific_Ocean_-_en.png/320px-Pacific_Ocean_-_en.png',
            'المحيط الأطلسي': 'https://upload.wikimedia.org/wikipedia/commons/thumb/f/f4/Atlantic_Ocean_-_en.png/320px-Atlantic_Ocean_-_en.png',
            'جبال الهملايا': 'https://upload.wikimedia.org/wikipedia/commons/thumb/d/d1/Mount_Everest_as_seen_from_Drukair2_PLW_edit.jpg/320px-Mount_Everest_as_seen_from_Drukair2_PLW_edit.jpg',
            'جبال الألب': 'https://upload.wikimedia.org/wikipedia/commons/thumb/d/d9/Aiguilles_de_Chamonix_and_Mont_Blanc.jpg/320px-Aiguilles_de_Chamonix_and_Mont_Blanc.jpg',
            'نهر النيل': 'https://upload.wikimedia.org/wikipedia/commons/thumb/2/25/River_Nile_map.svg/320px-River_Nile_map.svg.png',
            'نهر الأمازون': 'https://upload.wikimedia.org/wikipedia/commons/thumb/6/6e/Amazon_River_Itacoatiara_Amazonas_Brazil.jpg/320px-Amazon_River_Itacoatiara_Amazonas_Brazil.jpg',
            'صحراء الربع الخالي': 'https://upload.wikimedia.org/wikipedia/commons/thumb/b/b0/Rub_al_Khali_002.JPG/320px-Rub_al_Khali_002.JPG',
            'الصحراء الكبرى': 'https://upload.wikimedia.org/wikipedia/commons/thumb/9/94/Sahara_satellite_hires.jpg/320px-Sahara_satellite_hires.jpg',
            'غابات الأمازون': 'https://upload.wikimedia.org/wikipedia/commons/thumb/a/a4/Amazon_Manaus_forest.jpg/320px-Amazon_Manaus_forest.jpg',
            'القارة القطبية الجنوبية': 'https://upload.wikimedia.org/wikipedia/commons/thumb/e/e8/Antarctica_6400px_from_Blue_Marble.jpg/320px-Antarctica_6400px_from_Blue_Marble.jpg',
            'القطب الشمالي': 'https://upload.wikimedia.org/wikipedia/commons/thumb/2/22/Pack_ice_slow_sunrise.jpg/320px-Pack_ice_slow_sunrise.jpg'
        };

        function renderCrossword(crosswordData) {
            const { grid, clues } = crosswordData;
            currentCrosswordGrid = grid;

            // Render the grid
            const gridElement = document.getElementById('crossword-grid');
            gridElement.innerHTML = '';
            gridElement.style.gridTemplateColumns = `repeat(${grid[0].length}, 28px)`;
            gridElement.style.gridTemplateRows = `repeat(${grid.length}, 28px)`;

            // Second pass: create the cells
            for (let i = 0; i < grid.length; i++) {
                for (let j = 0; j < grid[0].length; j++) {
                    const cell = grid[i][j];
                    const cellElement = document.createElement('div');
                    cellElement.className = cell.isActive ? 'crossword-cell' : 'crossword-cell empty';
                    cellElement.dataset.row = i;
                    cellElement.dataset.col = j;

                    // Add cell number if this is the start of a word
                    if (cell.clueNumber) {
                        const numberElement = document.createElement('div');
                        numberElement.className = 'crossword-cell-number';
                        numberElement.textContent = cell.clueNumber;
                        cellElement.appendChild(numberElement);

                        // Add question index as a symbol
                        if (cell.questionIndex !== undefined) {
                            cellElement.dataset.questionIndex = cell.questionIndex;

                            // Add a symbol based on the question index
                            const symbolElement = document.createElement('div');
                            symbolElement.className = 'crossword-cell-symbol';

                            // Use different symbols or colors based on question index
                            const symbols = ['★', '♦', '♣', '♥', '♠', '●', '■', '▲', '◆', '◈'];
                            const symbolIndex = cell.questionIndex % symbols.length;
                            symbolElement.textContent = symbols[symbolIndex];
                            symbolElement.style.color = `hsl(${(cell.questionIndex * 30) % 360}, 70%, 50%)`;

                            cellElement.appendChild(symbolElement);
                        }
                    }

                    gridElement.appendChild(cellElement);
                }
            }

            // Pre-fill the first two letters of each word
            const wordStarts = [];

            // Find all word starts
            clues.forEach(clue => {
                // Find the starting cell for this clue
                for (let i = 0; i < grid.length; i++) {
                    for (let j = 0; j < grid[0].length; j++) {
                        if (grid[i][j].clueNumber === clue.number &&
                            grid[i][j].direction === (clue.direction === 'أفقي' ? 'horizontal' : 'vertical')) {

                            wordStarts.push({
                                row: i,
                                col: j,
                                direction: clue.direction === 'أفقي' ? 'horizontal' : 'vertical',
                                questionIndex: clue.questionIndex
                            });
                            break;
                        }
                    }
                }
            });

            // Pre-fill the first two letters of each word
            wordStarts.forEach(start => {
                const { row, col, direction } = start;

                // Fill first letter
                let cell = document.querySelector(`.crossword-cell[data-row="${row}"][data-col="${col}"]`);
                if (cell) {
                    cell.textContent = grid[row][col].correctLetter;
                    cell.classList.add('filled');
                    cell.classList.add('prefilled');
                }

                // Fill second letter if it exists
                let secondRow = row;
                let secondCol = col;

                if (direction === 'horizontal') {
                    secondCol++;
                } else {
                    secondRow++;
                }

                // Check if the second cell is valid and part of the same word
                if (secondRow < grid.length && secondCol < grid[0].length && grid[secondRow][secondCol].isActive) {
                    cell = document.querySelector(`.crossword-cell[data-row="${secondRow}"][data-col="${secondCol}"]`);
                    if (cell) {
                        cell.textContent = grid[secondRow][secondCol].correctLetter;
                        cell.classList.add('filled');
                        cell.classList.add('prefilled');
                    }
                }
            });

            // Render the clues, grouped by question
            const cluesElement = document.getElementById('crossword-clues');
            cluesElement.innerHTML = '';

            // Group clues by question index
            const cluesByQuestion = {};
            clues.forEach(clue => {
                if (!cluesByQuestion[clue.questionIndex]) {
                    cluesByQuestion[clue.questionIndex] = [];
                }
                cluesByQuestion[clue.questionIndex].push(clue);
            });

            // Render question images
            const imagesContainer = document.getElementById('question-images');
            imagesContainer.innerHTML = '';

            // Create a container for each question's clues
            Object.keys(cluesByQuestion).forEach(questionIndex => {
                const questionClues = cluesByQuestion[questionIndex];
                const questionContainer = document.createElement('div');
                questionContainer.className = 'question-clues';

                // Add question header
                const questionHeader = document.createElement('h3');
                questionHeader.className = 'question-header';

                // Use the same symbol as in the grid
                const symbols = ['★', '♦', '♣', '♥', '♠', '●', '■', '▲', '◆', '◈'];
                const symbolIndex = parseInt(questionIndex) % symbols.length;
                const symbol = symbols[symbolIndex];
                const symbolColor = `hsl(${(parseInt(questionIndex) * 30) % 360}, 70%, 50%)`;

                questionHeader.innerHTML = `<span style="color: ${symbolColor}">${symbol}</span> السؤال ${parseInt(questionIndex) + 1}`;
                questionContainer.appendChild(questionHeader);

                // Add clues for this question
                questionClues.forEach(clue => {
                    const clueElement = document.createElement('div');
                    clueElement.className = 'crossword-clue';
                    clueElement.textContent = `${clue.number}. ${clue.direction}: ${clue.text}`;
                    clueElement.dataset.clueNumber = clue.number;
                    clueElement.dataset.questionIndex = questionIndex;

                    // Add click handler to highlight the corresponding word
                    clueElement.addEventListener('click', () => highlightClue(clue.number, clue.direction === 'أفقي' ? 'horizontal' : 'vertical'));

                    questionContainer.appendChild(clueElement);

                    // Try to find an image for this question
                    const questionText = clue.text.split(' ')[0]; // Get the first word of the question
                    if (!document.querySelector(`.question-image[data-question-index="${questionIndex}"]`)) {
                        // Look for an image that matches any part of the question text
                        let imageUrl = null;
                        for (const [keyword, url] of Object.entries(questionImages)) {
                            if (clue.text.includes(keyword) || clue.word.includes(keyword)) {
                                imageUrl = url;
                                break;
                            }
                        }

                        if (imageUrl) {
                            // Create image container
                            const imageContainer = document.createElement('div');
                            imageContainer.className = 'question-image';
                            imageContainer.dataset.questionIndex = questionIndex;

                            // Create image element
                            const img = document.createElement('img');
                            img.src = imageUrl;
                            img.alt = `صورة للسؤال ${parseInt(questionIndex) + 1}`;

                            // Create caption with the same symbol
                            const caption = document.createElement('div');
                            caption.className = 'question-image-caption';
                            caption.innerHTML = `<span style="color: ${symbolColor}">${symbol}</span> السؤال ${parseInt(questionIndex) + 1}`;

                            // Add to containers
                            imageContainer.appendChild(img);
                            imageContainer.appendChild(caption);
                            imagesContainer.appendChild(imageContainer);
                        }
                    }
                });

                cluesElement.appendChild(questionContainer);
            });

            // Create letter tiles
            renderLetterTiles();
        }

        function highlightClue(clueNumber, direction) {
            // Remove highlight from all cells
            document.querySelectorAll('.crossword-cell').forEach(cell => {
                cell.classList.remove('highlighted');
            });

            // Remove active class from all clues
            document.querySelectorAll('.crossword-clue').forEach(clue => {
                clue.classList.remove('active');
            });

            // Add active class to the clicked clue
            const clueElement = document.querySelector(`.crossword-clue[data-clue-number="${clueNumber}"]`);
            if (clueElement) {
                clueElement.classList.add('active');
            }

            // Find the starting cell for this clue
            for (let i = 0; i < currentCrosswordGrid.length; i++) {
                for (let j = 0; j < currentCrosswordGrid[0].length; j++) {
                    if (currentCrosswordGrid[i][j].clueNumber === clueNumber &&
                        currentCrosswordGrid[i][j].direction === direction) {

                        // Highlight all cells in this word
                        let row = i;
                        let col = j;

                        while (row < currentCrosswordGrid.length && col < currentCrosswordGrid[0].length &&
                               currentCrosswordGrid[row][col].isActive) {

                            const cell = document.querySelector(`.crossword-cell[data-row="${row}"][data-col="${col}"]`);
                            if (cell) {
                                cell.classList.add('highlighted');
                            }

                            if (direction === 'horizontal') {
                                col++;
                            } else {
                                row++;
                            }
                        }

                        return;
                    }
                }
            }
        }

        function renderLetterTiles() {
            const lettersElement = document.getElementById('crossword-letters');
            lettersElement.innerHTML = '';

            // Get all cells that need to be filled (not pre-filled)
            const activeCells = document.querySelectorAll('.crossword-cell:not(.empty):not(.prefilled)');

            // Mark all empty cells as droppable
            activeCells.forEach(cell => {
                cell.classList.add('droppable');
                setupDropTarget(cell);
            });

            // Collect the correct letters for these cells
            const neededLetters = [];
            activeCells.forEach(cell => {
                const row = parseInt(cell.dataset.row);
                const col = parseInt(cell.dataset.col);
                const letter = currentCrosswordGrid[row][col].correctLetter;
                if (letter && letter.trim()) {
                    neededLetters.push(letter);
                }
            });

            // If we don't have enough letters, add some common Arabic letters
            if (neededLetters.length < 5) {
                const commonLetters = 'ابتثجحخدذرزسشصضطظعغفقكلمنهوي';
                for (let i = 0; i < 10; i++) {
                    neededLetters.push(commonLetters[Math.floor(Math.random() * commonLetters.length)]);
                }
            }

            // Shuffle the letters
            const shuffledLetters = neededLetters.sort(() => Math.random() - 0.5);

            // Create letter tiles
            shuffledLetters.forEach((letter, index) => {
                if (letter && letter.trim()) { // Make sure we have a valid letter
                    const tileElement = document.createElement('div');
                    tileElement.className = 'letter-tile';
                    tileElement.textContent = letter;
                    tileElement.dataset.index = index;
                    tileElement.draggable = true;
                    setupDraggableTile(tileElement);
                    lettersElement.appendChild(tileElement);
                }
            });
        }

        function setupDraggableTile(tile) {
            tile.addEventListener('dragstart', function(e) {
                // Add dragging class for visual feedback
                this.classList.add('dragging');

                // Set the data being dragged
                e.dataTransfer.setData('text/plain', this.textContent);
                e.dataTransfer.setData('application/tile-index', this.dataset.index);

                // Set the drag image (the tile itself)
                e.dataTransfer.effectAllowed = 'move';
            });

            tile.addEventListener('dragend', function() {
                // Remove dragging class when drag ends
                this.classList.remove('dragging');
            });
        }

        function setupDropTarget(cell) {
            // Prevent default to allow drop
            cell.addEventListener('dragover', function(e) {
                e.preventDefault();
                this.classList.add('drag-over');
            });

            // Handle drag leave
            cell.addEventListener('dragleave', function() {
                this.classList.remove('drag-over');
            });

            // Handle drop
            cell.addEventListener('drop', function(e) {
                e.preventDefault();
                this.classList.remove('drag-over');

                // Get the dragged data
                const letter = e.dataTransfer.getData('text/plain');
                const tileIndex = e.dataTransfer.getData('application/tile-index');

                // Find the tile that was dragged
                const tile = document.querySelector(`.letter-tile[data-index="${tileIndex}"]`);

                // Only proceed if the cell is empty and the tile is not already used
                if (!this.classList.contains('filled') && !tile.classList.contains('used')) {
                    // Fill the cell with the letter
                    this.textContent = letter;
                    this.classList.add('filled');
                    this.dataset.letterIndex = tileIndex;

                    // Mark the tile as used
                    tile.classList.add('used');

                    // Track the selected cell
                    const row = parseInt(this.dataset.row);
                    const col = parseInt(this.dataset.col);
                    selectedCells.push({ row, col, letterIndex: tileIndex });

                    // Check if the crossword is complete
                    checkCrosswordCompletion();
                }
            });
        }

        // Function to find the next empty cell in the grid

        function findNextEmptyCell() {
            // Skip both filled cells and pre-filled cells
            const cells = document.querySelectorAll('.crossword-cell:not(.empty):not(.filled):not(.prefilled)');
            return cells.length > 0 ? cells[0] : null;
        }

        function checkCrosswordCompletion() {
            // Check for any active cells that are not filled or pre-filled
            const emptyCells = document.querySelectorAll('.crossword-cell:not(.empty):not(.filled):not(.prefilled)');
            if (emptyCells.length === 0) {
                // All cells are filled, enable the check button
                document.getElementById('crossword-check').disabled = false;
            }
        }

        function clearCrossword() {
            // Clear all filled cells except pre-filled ones
            const filledCells = document.querySelectorAll('.crossword-cell.filled:not(.prefilled)');
            filledCells.forEach(cell => {
                cell.textContent = '';
                cell.classList.remove('filled');
                cell.classList.remove('correct');
                cell.classList.remove('incorrect');
                delete cell.dataset.letterIndex;

                // Make sure the cell is droppable again
                cell.classList.add('droppable');
            });

            // Also remove correct/incorrect classes from pre-filled cells
            const prefilledCells = document.querySelectorAll('.crossword-cell.prefilled');
            prefilledCells.forEach(cell => {
                cell.classList.remove('correct');
                cell.classList.remove('incorrect');
            });

            // Reset all letter tiles
            const usedTiles = document.querySelectorAll('.letter-tile.used');
            usedTiles.forEach(tile => {
                tile.classList.remove('used');
                tile.classList.remove('dragging');
            });

            // Clear selected cells
            selectedCells = [];

            // Disable check button
            document.getElementById('crossword-check').disabled = true;
        }

        function checkCrosswordAnswer() {
            let correctCount = 0;
            let totalCells = 0;

            // Get all cells that should be filled (including empty ones that should have letters)
            const allCellsToCheck = document.querySelectorAll('.crossword-cell:not(.empty):not(.prefilled)');
            totalCells = allCellsToCheck.length;

            // If no cells have been filled, don't give a perfect score
            const filledCells = document.querySelectorAll('.crossword-cell.filled:not(.prefilled)');
            if (filledCells.length === 0) {
                // Don't allow empty submissions to get a perfect score
                correctCount = 0;
            } else {
                // Check each filled cell
                filledCells.forEach(cell => {
                    const row = parseInt(cell.dataset.row);
                    const col = parseInt(cell.dataset.col);
                    const userLetter = cell.textContent;
                    const correctLetter = currentCrosswordGrid[row][col].correctLetter;

                    if (userLetter === correctLetter) {
                        cell.classList.add('correct');
                        correctCount++;
                    } else {
                        cell.classList.add('incorrect');
                    }
                });
            }

            // Calculate score and show feedback
            const percentCorrect = Math.round((correctCount / totalCells) * 100);

            // Group results by question
            const questionResults = {};

            // Check each question's words
            document.querySelectorAll('.crossword-cell[data-question-index]').forEach(cell => {
                const questionIndex = cell.dataset.questionIndex;
                if (!questionResults[questionIndex]) {
                    questionResults[questionIndex] = {
                        correct: 0,
                        total: 0
                    };
                }

                // Only count cells that are part of this question's words
                if (cell.classList.contains('filled')) {
                    questionResults[questionIndex].total++;
                    if (cell.classList.contains('correct')) {
                        questionResults[questionIndex].correct++;
                    }
                }
            });

            // Generate feedback for each question
            let feedbackHTML = `<div class="overall-result">النتيجة الإجمالية: ${percentCorrect}%</div>`;
            feedbackHTML += '<div class="question-results">';

            Object.keys(questionResults).forEach(questionIndex => {
                const result = questionResults[questionIndex];
                const questionPercentage = Math.round((result.correct / result.total) * 100) || 0;

                // Use the same symbol as in the grid
                const symbols = ['★', '♦', '♣', '♥', '♠', '●', '■', '▲', '◆', '◈'];
                const symbolIndex = parseInt(questionIndex) % symbols.length;
                const symbol = symbols[symbolIndex];
                const symbolColor = `hsl(${(parseInt(questionIndex) * 30) % 360}, 70%, 50%)`;

                let resultIcon = '';
                if (questionPercentage === 100) {
                    resultIcon = '✅'; // Correct
                } else if (questionPercentage >= 70) {
                    resultIcon = '⚠️'; // Partially correct
                } else {
                    resultIcon = '❌'; // Incorrect
                }

                feedbackHTML += `
                    <div class="question-result">
                        <span style="color: ${symbolColor}">${symbol}</span>
                        السؤال ${parseInt(questionIndex) + 1}:
                        ${resultIcon} ${questionPercentage}%
                    </div>
                `;
            });

            feedbackHTML += '</div>';

            // Show feedback
            document.getElementById('crossword-result').innerHTML = feedbackHTML;

            // Disable check button and enable clear button
            document.getElementById('crossword-check').disabled = true;
            document.getElementById('crossword-clear').disabled = false;

            // Disable letter tiles
            const letterTiles = document.querySelectorAll('.letter-tile');
            letterTiles.forEach(tile => {
                tile.removeEventListener('click', handleLetterTileClick);
                tile.style.cursor = 'not-allowed';
            });
        }

        function loadCrosswordQuestion() {
            try {
                // Create a comprehensive crossword with all questions
                const crosswordData = createComprehensiveCrossword();
                renderCrossword(crosswordData);

                // Reset UI
                document.getElementById('crossword-result').textContent = '';
                document.getElementById('crossword-continue').style.display = 'none';
                document.getElementById('crossword-check').disabled = false;
                document.getElementById('crossword-clear').disabled = false;
                document.getElementById('crossword-skip').style.display = 'none'; // Hide skip button for comprehensive crossword

                // Update progress indicator
                document.getElementById('crossword-progress').textContent = 'امتحان الكلمات المتقاطعة الشامل';

                // Show the grid and letters
                document.getElementById('crossword-grid').style.display = 'grid';
                document.getElementById('crossword-letters').style.display = 'flex';
                document.getElementById('crossword-clues').style.display = 'block';
                document.getElementById('crossword-check').style.display = 'inline-block';
                document.getElementById('crossword-clear').style.display = 'inline-block';
            } catch (error) {
                console.error('Error loading comprehensive crossword:', error);
                document.getElementById('crossword-question-block').innerHTML = `
                    <h2>حدث خطأ في تحميل الكلمات المتقاطعة</h2>
                    <p>يرجى المحاولة مرة أخرى</p>
                `;
                document.getElementById('crossword-grid').style.display = 'none';
                document.getElementById('crossword-letters').style.display = 'none';
                document.getElementById('crossword-clues').style.display = 'none';
                document.getElementById('crossword-check').style.display = 'none';
                document.getElementById('crossword-clear').style.display = 'none';
                document.getElementById('crossword-skip').style.display = 'none';
                document.getElementById('crossword-continue').style.display = 'none';
            }
        }

        // Crossword Event Listeners
        document.getElementById('crossword-check').addEventListener('click', checkCrosswordAnswer);

        document.getElementById('crossword-clear').addEventListener('click', clearCrossword);

        document.getElementById('crossword-skip').addEventListener('click', function() {
            currentCrosswordQuestion++;
            if (currentCrosswordQuestion < geoQuestionsOriginal.length) {
                loadCrosswordQuestion();
            } else {
                document.getElementById('crossword-question-block').innerHTML = `
                    <h2>النتيجة النهائية: ${crosswordScore} من ${geoQuestionsOriginal.length}</h2>
                `;
                document.getElementById('crossword-grid').style.display = 'none';
                document.getElementById('crossword-letters').style.display = 'none';
                document.getElementById('crossword-clues').style.display = 'none';
                document.getElementById('crossword-check').style.display = 'none';
                document.getElementById('crossword-clear').style.display = 'none';
                document.getElementById('crossword-skip').style.display = 'none';
                document.getElementById('crossword-continue').style.display = 'none';
            }
        });

        document.getElementById('crossword-continue').addEventListener('click', function() {
            currentCrosswordQuestion++;
            if (currentCrosswordQuestion < geoQuestionsOriginal.length) {
                loadCrosswordQuestion();
            } else {
                document.getElementById('crossword-question-block').innerHTML = `
                    <h2>النتيجة النهائية: ${crosswordScore} من ${geoQuestionsOriginal.length}</h2>
                `;
                document.getElementById('crossword-grid').style.display = 'none';
                document.getElementById('crossword-letters').style.display = 'none';
                document.getElementById('crossword-clues').style.display = 'none';
                document.getElementById('crossword-check').style.display = 'none';
                document.getElementById('crossword-clear').style.display = 'none';
                document.getElementById('crossword-skip').style.display = 'none';
                document.getElementById('crossword-continue').style.display = 'none';
            }
        });

        document.getElementById('crossword-reset').addEventListener('click', function() {
            // For comprehensive crossword, just reload the current crossword
            document.getElementById('crossword-result').textContent = '';
            loadCrosswordQuestion();
        });

        // Add event listener for Enter key in the answer input
        document.getElementById("answer-input").addEventListener("keydown", function(event) {
            // Check if the key pressed was Enter
            if (event.key === "Enter" && !event.shiftKey) {
                // Prevent the default action (new line)
                event.preventDefault();

                // Only trigger submit if the submit button is not disabled
                if (!document.getElementById("geo-submit").disabled) {
                    // Simulate a click on the submit button
                    document.getElementById("geo-submit").click();
                }
            }
        });

        // Biology Quiz Data
        let bioQuestionsOriginal = [
            {
                question: "ماهي الروابط المميزة للمستوى البنائي الثالثي",
                answers: [
                    {
                        label: "أحد أنواع الروابط",
                        definition: "التكافئية"
                    },
                    {
                        label: "نوع آخر من الروابط",
                        definition: "غير التكافئية"
                    }
                ]
            },
            {
                question: "ما أنواع الروابط التكافئية",
                answers: [
                    {
                        label: "الإجابة الأولى",
                        definition: "الروابط ثنائية الكبريت"
                    },
                    {
                        label: "الإجابة الثانية",
                        definition: "الجسور ثنائية الكبريت"
                    }
                ]
            },
            {
                question: "ما أنواع الروابط غير التكافئية",
                answers: [
                    {
                        label: "النوع الأول",
                        definition: "الهيدروجينية"
                    },
                    {
                        label: "النوع الثاني",
                        definition: "الكارهة للماء"
                    },
                    {
                        label: "النوع الثالث",
                        definition: "الشاردية"
                    }
                ]
            },
            {
                question: "أين تتواجد كل هاته الروابط",
                answers: [
                    {
                        label: "الإجابة",
                        definition: "تتواجد بين جذور الأحماض الآمينية المتقابلة في السلسلة البيبتيدية"
                    }
                ]
            },
            {
                question: "كيف تحدد هاته الروابط ولأي درجة",
                answers: [
                    {
                        label: "الإجابة",
                        definition: "الروابط محددة بدقة (محددة وراثيا) عددا ونوعا وموضعا"
                    }
                ]
            }
        ];

        // Lesson content for each question
        const bioLessons = [
            // Lesson 1: Types of bonds in tertiary structure
            {
                title: "الروابط المميزة للمستوى البنائي الثالثي",
                content: `
                    <p>المستوى البنائي الثالثي للبروتين هو الشكل ثلاثي الأبعاد للسلسلة البيبتيدية المنفردة. يتم تثبيت هذا الشكل بواسطة نوعين رئيسيين من الروابط:</p>

                    <p><strong>1. الروابط التكافئية:</strong> وهي روابط قوية تتكون بين ذرات الكبريت في جذور الأحماض الأمينية المحتوية على الكبريت (مثل السيستين). تسمى هذه الروابط بالجسور ثنائية الكبريت أو الروابط ثنائية الكبريت، وهي تساهم بشكل كبير في استقرار البنية الثالثية للبروتين.</p>

                    <p><strong>2. الروابط غير التكافئية:</strong> وهي روابط أضعف من الروابط التكافئية، لكنها أكثر عدداً، وتشمل:</p>
                    <ul>
                        <li>الروابط الهيدروجينية: تتكون بين ذرة هيدروجين مرتبطة بذرة سالبة الكهربية وذرة أخرى سالبة الكهربية.</li>
                        <li>الروابط الكارهة للماء: تنشأ بين جذور الأحماض الأمينية غير القطبية التي تميل إلى التجمع معاً بعيداً عن الماء.</li>
                        <li>الروابط الشاردية (الأيونية): تتكون بين الشحنات الموجبة والسالبة في جذور الأحماض الأمينية المختلفة.</li>
                    </ul>

                    <p>هذه الروابط مجتمعة تحدد الشكل النهائي للبروتين وبالتالي وظيفته البيولوجية.</p>
                `
            },
            // Lesson 2: Types of covalent bonds
            {
                title: "أنواع الروابط التكافئية",
                content: `
                    <p>الروابط التكافئية في المستوى البنائي الثالثي للبروتين تتمثل أساساً في:</p>

                    <p><strong>1. الروابط ثنائية الكبريت:</strong> هي روابط تكافئية قوية تتكون بين مجموعتي الثيول (-SH) الموجودة في الحمض الأميني سيستين. عندما تتأكسد مجموعتي الثيول، تتكون رابطة ثنائية الكبريت (S-S) التي تربط بين أجزاء مختلفة من السلسلة البيبتيدية.</p>

                    <p><strong>2. الجسور ثنائية الكبريت:</strong> هي مصطلح آخر يستخدم للإشارة إلى نفس النوع من الروابط، حيث تشكل هذه الروابط "جسوراً" تربط بين مناطق متباعدة من السلسلة البيبتيدية، مما يساهم في استقرار البنية الثالثية للبروتين.</p>

                    <p>تتميز هذه الروابط بأنها:</p>
                    <ul>
                        <li>قوية جداً مقارنة بالروابط غير التكافئية</li>
                        <li>تتطلب طاقة عالية لكسرها</li>
                        <li>تساهم بشكل كبير في ثبات البروتين ومقاومته للحرارة والظروف القاسية</li>
                        <li>محددة وراثياً من حيث موقعها في السلسلة البيبتيدية</li>
                    </ul>

                    <p>تلعب هذه الروابط دوراً حاسماً في تحديد الشكل النهائي للبروتين، وبالتالي وظيفته البيولوجية.</p>
                `
            },
            // Lesson 3: Types of non-covalent bonds
            {
                title: "أنواع الروابط غير التكافئية",
                content: `
                    <p>الروابط غير التكافئية في المستوى البنائي الثالثي للبروتين تشمل ثلاثة أنواع رئيسية:</p>

                    <p><strong>1. الروابط الهيدروجينية:</strong> تتكون بين ذرة هيدروجين مرتبطة بذرة سالبة الكهربية (مثل الأكسجين أو النيتروجين) وذرة أخرى سالبة الكهربية. هذه الروابط ضعيفة نسبياً لكنها كثيرة العدد في البروتين، مما يجعلها تساهم بشكل كبير في استقرار البنية الثالثية.</p>

                    <p><strong>2. الروابط الكارهة للماء (التفاعلات الهيدروفوبية):</strong> تنشأ بين جذور الأحماض الأمينية غير القطبية (الهيدروفوبية) التي تميل إلى التجمع معاً بعيداً عن الماء. هذه التفاعلات تدفع الأجزاء الكارهة للماء نحو داخل البروتين، بينما تبقى الأجزاء المحبة للماء على السطح الخارجي.</p>

                    <p><strong>3. الروابط الشاردية (الأيونية):</strong> تتكون بين المجموعات المشحونة بشحنات متعاكسة في جذور الأحماض الأمينية المختلفة. على سبيل المثال، تتكون رابطة أيونية بين مجموعة الكربوكسيل السالبة (-COO-) في حمض أميني وبين مجموعة الأمين الموجبة (-NH3+) في حمض أميني آخر.</p>

                    <p>رغم أن كل رابطة غير تكافئية على حدة تكون ضعيفة، إلا أن وجودها بأعداد كبيرة يجعلها تساهم بشكل فعال في استقرار البنية الثالثية للبروتين.</p>
                `
            },
            // Lesson 4: Where bonds are located
            {
                title: "موقع الروابط في البروتين",
                content: `
                    <p>تتواجد الروابط المميزة للمستوى البنائي الثالثي بين جذور الأحماض الأمينية المتقابلة في السلسلة البيبتيدية. هذه المواقع محددة بدقة وتختلف حسب نوع الرابطة:</p>

                    <p><strong>1. الروابط التكافئية (ثنائية الكبريت):</strong> تتواجد بين جذور الأحماض الأمينية التي تحتوي على الكبريت، وخاصة السيستين. هذه الروابط تربط بين أجزاء متباعدة من السلسلة البيبتيدية، مما يساعد على طي البروتين في شكله الثلاثي الأبعاد.</p>

                    <p><strong>2. الروابط الهيدروجينية:</strong> تتواجد بين ذرات الهيدروجين المرتبطة بذرات سالبة الكهربية (مثل الأكسجين والنيتروجين) وذرات أخرى سالبة الكهربية في جذور الأحماض الأمينية المختلفة.</p>

                    <p><strong>3. الروابط الكارهة للماء:</strong> تتواجد بين جذور الأحماض الأمينية غير القطبية (الهيدروفوبية) التي تميل إلى التجمع معاً في داخل البروتين بعيداً عن الماء.</p>

                    <p><strong>4. الروابط الشاردية:</strong> تتواجد بين المجموعات المشحونة بشحنات متعاكسة في جذور الأحماض الأمينية المختلفة، مثل مجموعات الكربوكسيل السالبة ومجموعات الأمين الموجبة.</p>

                    <p>هذه الروابط لا تتوزع بشكل عشوائي، بل تتواجد في مواقع محددة تساهم في تشكيل البنية الثلاثية الأبعاد للبروتين وتحديد وظيفته البيولوجية.</p>
                `
            },
            // Lesson 5: How bonds are determined
            {
                title: "كيفية تحديد الروابط ودرجة دقتها",
                content: `
                    <p>الروابط في المستوى البنائي الثالثي للبروتين محددة بدقة عالية، وهذا التحديد يكون على ثلاثة مستويات:</p>

                    <p><strong>1. محددة وراثياً:</strong> تسلسل الأحماض الأمينية في البروتين (المستوى البنائي الأولي) يتم تحديده بواسطة الجينات في الحمض النووي DNA. هذا التسلسل يحدد بدوره أماكن وأنواع الروابط التي ستتشكل في المستوى البنائي الثالثي.</p>

                    <p><strong>2. محددة عدداً:</strong> عدد الروابط في كل بروتين محدد بدقة ويعتمد على:</p>
                    <ul>
                        <li>عدد الأحماض الأمينية التي تحتوي على مجموعات قادرة على تكوين روابط معينة (مثل السيستين للروابط ثنائية الكبريت)</li>
                        <li>طول السلسلة البيبتيدية</li>
                        <li>الشكل النهائي للبروتين الذي يسمح بتقارب جذور أحماض أمينية معينة</li>
                    </ul>

                    <p><strong>3. محددة نوعاً:</strong> أنواع الروابط التي تتشكل تعتمد على طبيعة جذور الأحماض الأمينية المتقابلة:</p>
                    <ul>
                        <li>الروابط ثنائية الكبريت تتشكل فقط بين جذور السيستين</li>
                        <li>الروابط الهيدروجينية تتشكل بين المجموعات القطبية</li>
                        <li>الروابط الكارهة للماء تتشكل بين المجموعات غير القطبية</li>
                        <li>الروابط الشاردية تتشكل بين المجموعات المشحونة بشحنات متعاكسة</li>
                    </ul>

                    <p><strong>4. محددة موضعاً:</strong> مواقع الروابط محددة بدقة وتعتمد على الطي الطبيعي للبروتين الذي يجعل جذور أحماض أمينية معينة متقابلة ومناسبة لتكوين روابط محددة.</p>

                    <p>هذه الدقة في تحديد الروابط ضرورية لضمان أن يتخذ البروتين الشكل الصحيح الذي يمكنه من أداء وظيفته البيولوجية بكفاءة.</p>
                `
            }
        ];

        // Use original question order (no shuffling)
        let bioQuestions = [...bioQuestionsOriginal];
        let currentBioQuestion = 0;
        let bioScore = 0;
        let currentBioQuestionHintLevel = 0;
        let bioHintPenalty = 0.05; // 5% penalty for using a hint (more lenient)

        // Function to load biology questions
        function loadBiologyQuestion() {
            const question = bioQuestions[currentBioQuestion];
            document.getElementById("bio-question").textContent = question.question;

            // Get the answers container
            const answersBlock = document.getElementById("bio-answers-block");
            const multiAnswerContainer = document.querySelector(".multi-answer-container");

            // Clear previous answer sections
            multiAnswerContainer.innerHTML = "";

            // Add helper message for the first question
            if (question.question.includes("المستوى البنائي الثالثي")) {
                const helperMessage = document.createElement("div");
                helperMessage.className = "helper-message";
                helperMessage.innerHTML = "<strong>ملاحظة:</strong> يمكنك كتابة أي من الروابط (التكافئية أو غير التكافئية) في أي من الحقلين.";
                helperMessage.style.color = "#8b4513";
                helperMessage.style.backgroundColor = "rgba(245, 231, 193, 0.5)";
                helperMessage.style.padding = "5px";
                helperMessage.style.borderRadius = "5px";
                helperMessage.style.marginBottom = "10px";
                helperMessage.style.textAlign = "center";
                multiAnswerContainer.appendChild(helperMessage);
            }

            // Create a shuffled array of indices for the answers
            // This will randomize the order of answers but keep track of their original positions
            let answerIndices = [];
            for (let i = 0; i < question.answers.length; i++) {
                answerIndices.push(i);
            }

            // Don't shuffle for the first question about tertiary structure
            // This ensures "التكافئية" and "غير التكافئية" stay in their specific order
            if (!question.question.includes("المستوى البنائي الثالثي")) {
                answerIndices = shuffleArray(answerIndices);
            }

            // Create answer sections based on the shuffled indices
            for (let i = 0; i < answerIndices.length; i++) {
                const originalIndex = answerIndices[i];
                const answerSection = document.createElement("div");
                answerSection.className = "answer-section";

                const label = document.createElement("label");
                label.id = `bio-answer${i+1}-label`;
                label.className = "answer-label";
                label.textContent = question.answers[originalIndex].label;

                // Store the original index as a data attribute for later reference
                answerSection.dataset.originalIndex = originalIndex;

                const textarea = document.createElement("textarea");
                textarea.id = `bio-answer${i+1}-input`;
                textarea.className = "bio-answer-input";
                textarea.placeholder = "اكتب إجابتك هنا...";
                textarea.dataset.originalIndex = originalIndex; // Store original index in the textarea

                // Add Enter key handler to the new textarea
                textarea.addEventListener("keydown", function(event) {
                    if (event.key === "Enter" && !event.shiftKey) {
                        event.preventDefault();
                        if (!document.getElementById("bio-submit").disabled) {
                            document.getElementById("bio-submit").click();
                        }
                    }
                });

                const resultDiv = document.createElement("div");
                resultDiv.id = `bio-result${i+1}`;
                resultDiv.className = "answer-result";
                resultDiv.dataset.originalIndex = originalIndex; // Store original index in the result div

                answerSection.appendChild(label);
                answerSection.appendChild(textarea);
                answerSection.appendChild(resultDiv);

                multiAnswerContainer.appendChild(answerSection);
            }

            // Clear overall result
            document.getElementById("bio-result").textContent = "";

            // Reset UI state
            document.getElementById("bio-continue").style.display = "none";
            document.getElementById("bio-retry").style.display = "none";
            document.getElementById("bio-submit").disabled = false;
            document.getElementById("bio-skip").disabled = false;
            document.getElementById("bio-hint").disabled = false;
            document.getElementById("bio-learn").textContent = "عرض الإجابة/تعلم";

            // Hide lesson container if it exists
            const lessonContainer = document.getElementById("bio-lesson-container");
            if (lessonContainer) {
                lessonContainer.style.display = "none";
            }

            // Reset hint usage for the new question
            currentBioQuestionHintLevel = 0;

            // Update progress indicator
            const progressText = `السؤال ${currentBioQuestion + 1} من ${bioQuestions.length}`;
            document.getElementById("bio-progress").textContent = progressText;

            // Update the image based on the question content
            const imageElement = document.getElementById("bio-image");
            const captionElement = document.getElementById("bio-image-caption");

            // Match question content to determine which image to show
            const questionText = question.question.toLowerCase();

            if (questionText.includes("المستوى البنائي الثالثي")) {
                // Tertiary structure bonds
                imageElement.src = "https://upload.wikimedia.org/wikipedia/commons/thumb/c/c9/Protein_domains.svg/320px-Protein_domains.svg.png";
                imageElement.alt = "المستوى البنائي الثالثي";
                captionElement.textContent = "الرسم التوضيحي: المستوى البنائي الثالثي للبروتين";
            } else if (questionText.includes("الروابط التكافئية")) {
                // Covalent bonds
                imageElement.src = "https://upload.wikimedia.org/wikipedia/commons/thumb/f/f3/Disulfide-bond-formation.png/320px-Disulfide-bond-formation.png";
                imageElement.alt = "الروابط التكافئية";
                captionElement.textContent = "الرسم التوضيحي: الروابط ثنائية الكبريت (الجسور الكبريتية)";
            } else if (questionText.includes("الروابط غير التكافئية")) {
                // Non-covalent bonds
                imageElement.src = "https://upload.wikimedia.org/wikipedia/commons/thumb/c/c6/Hydrogen-bonding-in-water-2D.png/320px-Hydrogen-bonding-in-water-2D.png";
                imageElement.alt = "الروابط غير التكافئية";
                captionElement.textContent = "الرسم التوضيحي: الروابط الهيدروجينية والكارهة للماء والشاردية";
            } else if (questionText.includes("أين تتواجد")) {
                // Where bonds are located
                imageElement.src = "https://upload.wikimedia.org/wikipedia/commons/thumb/5/5d/Protein-structure.png/320px-Protein-structure.png";
                imageElement.alt = "موقع الروابط في البروتين";
                captionElement.textContent = "الرسم التوضيحي: جذور الأحماض الأمينية في السلسلة البيبتيدية";
            } else if (questionText.includes("كيف تحدد")) {
                // How bonds are determined
                imageElement.src = "https://upload.wikimedia.org/wikipedia/commons/thumb/0/0b/DNA_replication_en.svg/320px-DNA_replication_en.svg.png";
                imageElement.alt = "تحديد الروابط وراثيا";
                captionElement.textContent = "الرسم التوضيحي: التحديد الوراثي للروابط في البروتين";
            }
        }

        // Submit button for biology
        document.getElementById("bio-submit").addEventListener("click", function() {
            const question = bioQuestions[currentBioQuestion];
            const numAnswers = question.answers.length;

            // Arrays to store user answers, correct answers, similarities, and scores
            const userAnswers = [];
            const correctAnswers = [];
            const similarities = [];
            const feedbacks = [];
            const scoresToAdd = [];

            // Collect all answers and calculate similarities
            for (let i = 0; i < numAnswers; i++) {
                const inputElement = document.getElementById(`bio-answer${i+1}-input`);
                if (inputElement) {
                    // Get the original index from the data attribute
                    const originalIndex = parseInt(inputElement.dataset.originalIndex);

                    userAnswers[i] = inputElement.value.trim();
                    correctAnswers[i] = question.answers[originalIndex].definition;
                    similarities[i] = calculateSimilarity(userAnswers[i], correctAnswers[i]);

                    // For the first question about tertiary structure, check both possible answers
                    let bestSimilarity = similarities[i];
                    let bestScore = 0;
                    let bestFeedback = "";

                    // For the first question about tertiary structure bonds, check against both possible answers
                    if (question.question.includes("المستوى البنائي الثالثي")) {
                        // Check similarity against both possible correct answers
                        for (let j = 0; j < question.answers.length; j++) {
                            const possibleAnswer = question.answers[j].definition;
                            const similarityToAnswer = calculateSimilarity(userAnswers[i], possibleAnswer);

                            // If this is a better match than the current one
                            if (similarityToAnswer > bestSimilarity) {
                                bestSimilarity = similarityToAnswer;

                                // Evaluate based on the best similarity
                                if (bestSimilarity >= 80) {
                                    bestScore = 1 / numAnswers;
                                    bestFeedback = "✅ إجابة صحيحة";
                                } else if (bestSimilarity >= 50) {
                                    bestScore = 0.5 / numAnswers;
                                    bestFeedback = "⚠️ إجابة صحيحة جزئياً";
                                } else {
                                    bestScore = 0;
                                    bestFeedback = "❌ إجابة غير صحيحة";
                                }
                            }
                        }

                        // Use the best score and feedback
                        similarities[i] = bestSimilarity;
                        scoresToAdd[i] = bestScore;
                        feedbacks[i] = bestFeedback;

                        // Skip the regular evaluation for this question
                        continue;
                    }

                    // Evaluate each answer (for questions other than the first one)
                    if (similarities[i] >= 80) {
                        scoresToAdd[i] = 1 / numAnswers; // Each answer is worth an equal portion of the total
                        feedbacks[i] = "✅ إجابة صحيحة";
                    } else if (similarities[i] >= 50) {
                        scoresToAdd[i] = 0.5 / numAnswers; // Half credit for partial answer
                        feedbacks[i] = "⚠️ إجابة صحيحة جزئياً";
                    } else {
                        scoresToAdd[i] = 0;
                        feedbacks[i] = "❌ إجابة غير صحيحة";
                    }

                    // Apply hint penalty if hints were used
                    if (currentBioQuestionHintLevel > 0 && scoresToAdd[i] > 0) {
                        const totalPenalty = bioHintPenalty * currentBioQuestionHintLevel;
                        const individualPenalty = totalPenalty / numAnswers;
                        scoresToAdd[i] = Math.max(0, scoresToAdd[i] - individualPenalty);
                        feedbacks[i] += ` (تم خصم ${Math.round(individualPenalty * 100)}% لاستخدام التلميح)`;
                    }
                }
            }

            // Calculate total score for this question
            const totalScore = scoresToAdd.reduce((sum, score) => sum + score, 0);
            bioScore += totalScore;

            // Show feedback for each answer
            for (let i = 0; i < numAnswers; i++) {
                const resultElement = document.getElementById(`bio-result${i+1}`);
                if (resultElement) {
                    resultElement.innerHTML = `
                        <div>${feedbacks[i]}</div>
                        <div class="correct-answer">
                            <strong>الإجابة الصحيحة:</strong> ${correctAnswers[i]}
                        </div>
                    `;
                }

                // Disable all input fields
                const inputElement = document.getElementById(`bio-answer${i+1}-input`);
                if (inputElement) {
                    inputElement.disabled = true;
                }
            }

            // Show overall score
            const overallFeedback = totalScore >= 0.8 ? "✅ ممتاز!" :
                                   totalScore >= 0.5 ? "⚠️ جيد" : "❌ يحتاج تحسين";

            document.getElementById("bio-result").innerHTML = `
                <div class="overall-result">
                    <strong>النتيجة الإجمالية:</strong> ${overallFeedback} (${Math.round(totalScore * 100)}%)
                </div>
            `;

            // Disable interaction and show continue button
            document.getElementById("bio-submit").disabled = true;
            document.getElementById("bio-skip").disabled = true;
            document.getElementById("bio-hint").disabled = true;
            document.getElementById("bio-continue").style.display = "inline-block";
            document.getElementById("bio-retry").style.display = "inline-block";
        });

        // Hint button for biology
        document.getElementById("bio-hint").addEventListener("click", function() {
            // Increment hint level for this question
            currentBioQuestionHintLevel++;

            const question = bioQuestions[currentBioQuestion];
            const numAnswers = question.answers.length;

            // Calculate total penalty
            const totalPenalty = bioHintPenalty * currentBioQuestionHintLevel;

            // Track maximum hint level across all answers
            let maxTotalLetters = 0;

            // Generate and display hints for each answer
            for (let i = 0; i < numAnswers; i++) {
                const resultElement = document.getElementById(`bio-result${i+1}`);
                if (resultElement) {
                    // Get the original index from the data attribute
                    const originalIndex = parseInt(resultElement.dataset.originalIndex);
                    const correctAnswer = question.answers[originalIndex].definition;

                    // Generate smart hint using our new function
                    const hint = createSmartHint(correctAnswer, currentBioQuestionHintLevel);

                    // Display the hint
                    resultElement.innerHTML = `
                        <div class="hint-container">
                            <strong>تلميح (${currentBioQuestionHintLevel}):</strong> ${hint}
                        </div>
                    `;

                    // Calculate total letters in this answer
                    const words = correctAnswer.split(' ');
                    const totalLetters = words.reduce((sum, word) => sum + word.length, 0);
                    maxTotalLetters = Math.max(maxTotalLetters, totalLetters);
                }
            }

            // Display penalty information
            document.getElementById("bio-result").innerHTML = `
                <div class="hint-container">
                    <p class="hint-penalty">سيتم خصم ${totalPenalty * 100}% من نقاطك لهذا السؤال</p>
                </div>
            `;

            // Disable the hint button if we've revealed a significant number of letters
            // This is a more conservative approach to prevent revealing too much
            const averageWordLength = 5; // Estimate of average word length
            const estimatedMaxHints = Math.min(20, maxTotalLetters); // Cap at 20 to prevent excessive hints

            if (currentBioQuestionHintLevel >= estimatedMaxHints) {
                document.getElementById("bio-hint").disabled = true;
            }
        });

        // Skip button for biology
        document.getElementById("bio-skip").addEventListener("click", function() {
            currentBioQuestion++;
            if (currentBioQuestion < bioQuestions.length) {
                loadBiologyQuestion();
            } else {
                document.getElementById("bio-question").textContent = `النتيجة النهائية: ${bioScore} من ${bioQuestions.length}`;
                document.getElementById("bio-answers-block").style.display = "none";
                document.getElementById("bio-submit").style.display = "none";
                document.getElementById("bio-skip").style.display = "none";
                document.getElementById("bio-hint").style.display = "none";
                document.getElementById("bio-retry").style.display = "none";
                document.getElementById("bio-continue").style.display = "none";
            }
        });

        // Continue button for biology
        document.getElementById("bio-continue").addEventListener("click", function() {
            currentBioQuestion++;
            if (currentBioQuestion < bioQuestions.length) {
                loadBiologyQuestion();
            } else {
                document.getElementById("bio-question").textContent = `النتيجة النهائية: ${bioScore} من ${bioQuestions.length}`;
                document.getElementById("bio-answers-block").style.display = "none";
                document.getElementById("bio-submit").style.display = "none";
                document.getElementById("bio-skip").style.display = "none";
                document.getElementById("bio-hint").style.display = "none";
                document.getElementById("bio-retry").style.display = "none";
                document.getElementById("bio-continue").style.display = "none";
            }
        });

        // Retry button for biology
        document.getElementById("bio-retry").addEventListener("click", function() {
            const question = bioQuestions[currentBioQuestion];
            const numAnswers = question.answers.length;

            // Clear and enable all answer inputs and results
            for (let i = 0; i < numAnswers; i++) {
                const inputElement = document.getElementById(`bio-answer${i+1}-input`);
                const resultElement = document.getElementById(`bio-result${i+1}`);

                if (inputElement) {
                    inputElement.value = "";
                    inputElement.disabled = false;
                }

                if (resultElement) {
                    resultElement.innerHTML = "";
                }
            }

            // Clear overall result and reset UI state
            document.getElementById("bio-result").innerHTML = "";
            document.getElementById("bio-continue").style.display = "none";
            document.getElementById("bio-retry").style.display = "none";
            document.getElementById("bio-submit").disabled = false;
            document.getElementById("bio-skip").disabled = false;
            document.getElementById("bio-hint").disabled = false;

            // Reset hint usage for retrying the question
            currentBioQuestionHintLevel = 0;
        });

        // Reset button for biology
        document.getElementById("bio-reset").addEventListener("click", function() {
            // Reset to original questions without shuffling
            bioQuestions = [...bioQuestionsOriginal];
            currentBioQuestion = 0;
            bioScore = 0;
            document.getElementById("bio-answers-block").style.display = "block";
            document.getElementById("bio-submit").style.display = "inline-block";
            document.getElementById("bio-skip").style.display = "inline-block";
            document.getElementById("bio-hint").style.display = "inline-block";
            loadBiologyQuestion();
        });

        // Add event listener for Enter key in the biology answer inputs
        function addEnterKeyHandler(inputId) {
            document.getElementById(inputId).addEventListener("keydown", function(event) {
                // Check if the key pressed was Enter
                if (event.key === "Enter" && !event.shiftKey) {
                    // Prevent the default action (new line)
                    event.preventDefault();

                    // Only trigger submit if the submit button is not disabled
                    if (!document.getElementById("bio-submit").disabled) {
                        // Simulate a click on the submit button
                        document.getElementById("bio-submit").click();
                    }
                }
            });
        }

        // Learn button for biology
        document.getElementById("bio-learn").addEventListener("click", function() {
            const question = bioQuestions[currentBioQuestion];
            const lesson = bioLessons[currentBioQuestion];

            // Create lesson container if it doesn't exist
            let lessonContainer = document.getElementById("bio-lesson-container");
            if (!lessonContainer) {
                lessonContainer = document.createElement("div");
                lessonContainer.id = "bio-lesson-container";
                lessonContainer.className = "lesson-container";
                document.getElementById("bio-result").parentNode.insertBefore(lessonContainer, document.getElementById("bio-result"));
            }

            // Toggle lesson visibility
            if (lessonContainer.style.display === "block") {
                lessonContainer.style.display = "none";
                document.getElementById("bio-learn").textContent = "عرض الإجابة/تعلم";
            } else {
                // Update lesson content
                lessonContainer.innerHTML = `
                    <div class="lesson-title">${lesson.title}</div>
                    <div class="lesson-content">${lesson.content}</div>
                `;
                lessonContainer.style.display = "block";
                document.getElementById("bio-learn").textContent = "إخفاء الدرس";

                // Disable the submit button to prevent cheating
                document.getElementById("bio-submit").disabled = true;
            }
        });

        // Add Enter key handlers to both input fields
        addEnterKeyHandler("bio-answer1-input");
        addEnterKeyHandler("bio-answer2-input");

        // Redox Exercise: Half-Equations
        function initHalfEquationExercise() {
            const oxidationTarget = document.getElementById('oxidation-half-equation-target');
            const reductionTarget = document.getElementById('reduction-half-equation-target');
            const piecesContainer = document.getElementById('half-equation-pieces');
            const feedback = document.getElementById('half-equation-feedback');
            const checkButton = document.getElementById('check-half-equation');
            const resetButton = document.getElementById('reset-half-equation');

            // The correct answers - we'll check for multiple possible correct formats
            const correctOxidationAnswers = [
                "Fe(s)→Fe²⁺(aq)+2e⁻",
                "Fe(s) → Fe²⁺(aq) + 2e⁻",
                "Fe(s) → Fe²⁺ (aq) + 2e⁻",
                "Fe(s)→Fe²⁺(aq) + 2e⁻",
                "Fe(s) →Fe²⁺(aq) + 2e⁻",
                "Fe(s) → Fe²⁺(aq)+ 2e⁻",
                "Fe(s) → Fe²⁺(aq) +2e⁻",
                "Fe→Fe²⁺+2e⁻",
                "Fe→Fe²⁺(aq)+2e⁻"
            ];

            const correctReductionAnswers = [
                "Cu²⁺(aq)+2e⁻→Cu(s)",
                "Cu²⁺(aq) + 2e⁻ → Cu(s)",
                "Cu²⁺ (aq) + 2e⁻ → Cu(s)",
                "Cu²⁺(aq) +2e⁻ → Cu(s)",
                "Cu²⁺(aq)+ 2e⁻ → Cu(s)",
                "Cu²⁺(aq) + 2e⁻→ Cu(s)",
                "Cu²⁺(aq) + 2e⁻ →Cu(s)",
                "Cu²⁺+2e⁻→Cu",
                "Cu²⁺(aq)+2e⁻→Cu"
            ];

            // Initialize the pieces
            const pieces = piecesContainer.querySelectorAll('.redox-piece');
            pieces.forEach(piece => {
                // Make pieces draggable
                piece.addEventListener('dragstart', function(e) {
                    this.classList.add('dragging');
                    e.dataTransfer.setData('text/plain', this.dataset.value);
                    e.dataTransfer.effectAllowed = 'move';
                });

                piece.addEventListener('dragend', function() {
                    this.classList.remove('dragging');
                });
            });

            // Function to set up a target area
            function setupTarget(targetContainer, placeholderText) {
                targetContainer.addEventListener('dragover', function(e) {
                    e.preventDefault();
                    e.dataTransfer.dropEffect = 'move';
                });

                targetContainer.addEventListener('drop', function(e) {
                    e.preventDefault();

                    // Get the dragged piece value
                    const value = e.dataTransfer.getData('text/plain');

                    // Find the original piece
                    const originalPiece = piecesContainer.querySelector(`.redox-piece[data-value="${value}"]`);

                    // Only proceed if the piece is not already placed
                    if (originalPiece && !originalPiece.classList.contains('placed')) {
                        // Clear the placeholder text if it's the first piece
                        if (targetContainer.querySelector('p')) {
                            targetContainer.innerHTML = '';
                        }

                        // Create a new piece in the target area
                        const newPiece = document.createElement('div');
                        newPiece.className = 'redox-piece placed';
                        newPiece.textContent = originalPiece.textContent;
                        newPiece.dataset.value = value;
                        targetContainer.appendChild(newPiece);

                        // Mark the original piece as placed
                        originalPiece.classList.add('placed');

                        // Make the placed piece draggable back to the pieces container
                        newPiece.draggable = true;

                        newPiece.addEventListener('dragstart', function(e) {
                            this.classList.add('dragging');
                            e.dataTransfer.setData('text/plain', this.dataset.value);
                            e.dataTransfer.effectAllowed = 'move';
                        });

                        newPiece.addEventListener('dragend', function() {
                            this.classList.remove('dragging');
                        });

                        // Add double-click to remove
                        newPiece.addEventListener('dblclick', function() {
                            // Find the original piece and unmark it
                            const originalPiece = piecesContainer.querySelector(`.redox-piece[data-value="${this.dataset.value}"]`);
                            if (originalPiece) {
                                originalPiece.classList.remove('placed');
                            }

                            // Remove this piece
                            this.remove();

                            // If no pieces left, restore the placeholder
                            if (targetContainer.children.length === 0) {
                                targetContainer.innerHTML = `<p>${placeholderText}</p>`;
                            }

                            // Clear feedback
                            feedback.textContent = '';
                            feedback.className = 'redox-feedback';
                        });
                    }
                });
            }

            // Set up both target areas
            setupTarget(oxidationTarget, 'اسحب العناصر هنا لتكوين نصف معادلة الأكسدة');
            setupTarget(reductionTarget, 'اسحب العناصر هنا لتكوين نصف معادلة الاختزال');

            // Set up the pieces container to accept pieces back
            piecesContainer.addEventListener('dragover', function(e) {
                e.preventDefault();
                e.dataTransfer.dropEffect = 'move';
            });

            piecesContainer.addEventListener('drop', function(e) {
                e.preventDefault();

                // Get the dragged piece value
                const value = e.dataTransfer.getData('text/plain');

                // Find the placed piece in either target
                const oxidationPlacedPiece = oxidationTarget.querySelector(`.redox-piece[data-value="${value}"]`);
                const reductionPlacedPiece = reductionTarget.querySelector(`.redox-piece[data-value="${value}"]`);

                // Find the original piece
                const originalPiece = piecesContainer.querySelector(`.redox-piece[data-value="${value}"]`);

                // Handle piece from oxidation target
                if (oxidationPlacedPiece && originalPiece) {
                    // Remove the placed piece
                    oxidationPlacedPiece.remove();

                    // Unmark the original piece
                    originalPiece.classList.remove('placed');

                    // If no pieces left, restore the placeholder
                    if (oxidationTarget.children.length === 0) {
                        oxidationTarget.innerHTML = '<p>اسحب العناصر هنا لتكوين نصف معادلة الأكسدة</p>';
                    }

                    // Clear feedback
                    feedback.textContent = '';
                    feedback.className = 'redox-feedback';
                }

                // Handle piece from reduction target
                if (reductionPlacedPiece && originalPiece) {
                    // Remove the placed piece
                    reductionPlacedPiece.remove();

                    // Unmark the original piece
                    originalPiece.classList.remove('placed');

                    // If no pieces left, restore the placeholder
                    if (reductionTarget.children.length === 0) {
                        reductionTarget.innerHTML = '<p>اسحب العناصر هنا لتكوين نصف معادلة الاختزال</p>';
                    }

                    // Clear feedback
                    feedback.textContent = '';
                    feedback.className = 'redox-feedback';
                }
            });

            // Check button
            checkButton.addEventListener('click', function() {
                // Get the current equations
                const oxidationPieces = oxidationTarget.querySelectorAll('.redox-piece');
                const reductionPieces = reductionTarget.querySelectorAll('.redox-piece');

                if (oxidationPieces.length === 0 || reductionPieces.length === 0) {
                    feedback.textContent = 'يرجى تكوين كلا المعادلتين أولاً';
                    feedback.className = 'redox-feedback incorrect';
                    return;
                }

                // Build the equations
                let oxidationEquation = '';
                oxidationPieces.forEach(piece => {
                    oxidationEquation += piece.textContent;
                });

                let reductionEquation = '';
                reductionPieces.forEach(piece => {
                    reductionEquation += piece.textContent;
                });

                // Remove all spaces for comparison
                const cleanOxidationEquation = oxidationEquation.replace(/\s+/g, '');
                const cleanReductionEquation = reductionEquation.replace(/\s+/g, '');

                // Check if they're correct
                let isOxidationCorrect = false;
                for (const correctAnswer of correctOxidationAnswers) {
                    const cleanCorrectAnswer = correctAnswer.replace(/\s+/g, '');
                    if (cleanOxidationEquation === cleanCorrectAnswer) {
                        isOxidationCorrect = true;
                        break;
                    }
                }

                let isReductionCorrect = false;
                for (const correctAnswer of correctReductionAnswers) {
                    const cleanCorrectAnswer = correctAnswer.replace(/\s+/g, '');
                    if (cleanReductionEquation === cleanCorrectAnswer) {
                        isReductionCorrect = true;
                        break;
                    }
                }

                // Provide feedback based on correctness
                if (isOxidationCorrect && isReductionCorrect) {
                    feedback.textContent = 'إجابة صحيحة! أحسنت! كلا المعادلتين صحيحتان.';
                    feedback.className = 'redox-feedback correct';

                    // Disable dragging once correct
                    oxidationTarget.querySelectorAll('.redox-piece').forEach(piece => {
                        piece.draggable = false;
                    });
                    reductionTarget.querySelectorAll('.redox-piece').forEach(piece => {
                        piece.draggable = false;
                    });
                } else if (isOxidationCorrect) {
                    feedback.textContent = 'نصف معادلة الأكسدة صحيحة، لكن نصف معادلة الاختزال غير صحيحة.';
                    feedback.className = 'redox-feedback incorrect';
                } else if (isReductionCorrect) {
                    feedback.textContent = 'نصف معادلة الاختزال صحيحة، لكن نصف معادلة الأكسدة غير صحيحة.';
                    feedback.className = 'redox-feedback incorrect';
                } else {
                    feedback.textContent = 'كلا المعادلتين غير صحيحتين. حاول مرة أخرى.';
                    feedback.className = 'redox-feedback incorrect';
                }
            });

            // Reset button
            resetButton.addEventListener('click', function() {
                // Clear the targets
                oxidationTarget.innerHTML = '<p>اسحب العناصر هنا لتكوين نصف معادلة الأكسدة</p>';
                reductionTarget.innerHTML = '<p>اسحب العناصر هنا لتكوين نصف معادلة الاختزال</p>';

                // Unmark all pieces
                pieces.forEach(piece => {
                    piece.classList.remove('placed');
                });

                // Clear feedback
                feedback.textContent = '';
                feedback.className = 'redox-feedback';
            });
        }

        // Redox Exercise: Oxidizing and Reducing Agents
        function initOxRedExercise() {
            const oxidizingAgentTarget = document.getElementById('oxidizing-agent-target');
            const reducingAgentTarget = document.getElementById('reducing-agent-target');
            const piecesContainer = document.getElementById('agents-pieces');
            const feedback = document.getElementById('agents-feedback');
            const checkButton = document.getElementById('check-agents');
            const resetButton = document.getElementById('reset-agents');

            // The correct answers
            const correctOxidizingAgent = "CuSO₄(aq)";
            const correctReducingAgent = "Fe(s)";

            // Initialize the pieces
            const pieces = piecesContainer.querySelectorAll('.redox-piece');
            pieces.forEach(piece => {
                // Make pieces draggable
                piece.addEventListener('dragstart', function(e) {
                    this.classList.add('dragging');
                    e.dataTransfer.setData('text/plain', this.dataset.value);
                    e.dataTransfer.effectAllowed = 'move';
                });

                piece.addEventListener('dragend', function() {
                    this.classList.remove('dragging');
                });
            });

            // Function to set up a target area
            function setupTarget(targetContainer) {
                targetContainer.addEventListener('dragover', function(e) {
                    e.preventDefault();
                    e.dataTransfer.dropEffect = 'move';
                });

                targetContainer.addEventListener('drop', function(e) {
                    e.preventDefault();

                    // Get the dragged piece value
                    const value = e.dataTransfer.getData('text/plain');

                    // Clear the target first (only one answer allowed)
                    const existingPieces = targetContainer.querySelectorAll('.redox-piece');
                    existingPieces.forEach(piece => {
                        // Find the original piece and unmark it
                        const originalPiece = piecesContainer.querySelector(`.redox-piece[data-value="${piece.dataset.value}"]`);
                        if (originalPiece) {
                            originalPiece.classList.remove('placed');
                        }

                        // Remove the piece
                        piece.remove();
                    });

                    // Find the original piece
                    const originalPiece = piecesContainer.querySelector(`.redox-piece[data-value="${value}"]`);

                    // Only proceed if the piece is not already placed in another target
                    if (originalPiece && !originalPiece.classList.contains('placed')) {
                        // Clear the placeholder text
                        if (targetContainer.querySelector('p')) {
                            targetContainer.innerHTML = '';
                        }

                        // Create a new piece in the target area
                        const newPiece = document.createElement('div');
                        newPiece.className = 'redox-piece placed';
                        newPiece.textContent = originalPiece.textContent;
                        newPiece.dataset.value = value;
                        targetContainer.appendChild(newPiece);

                        // Mark the original piece as placed
                        originalPiece.classList.add('placed');

                        // Add double-click to remove
                        newPiece.addEventListener('dblclick', function() {
                            // Find the original piece and unmark it
                            const originalPiece = piecesContainer.querySelector(`.redox-piece[data-value="${this.dataset.value}"]`);
                            if (originalPiece) {
                                originalPiece.classList.remove('placed');
                            }

                            // Remove this piece
                            this.remove();

                            // Restore the placeholder
                            if (targetContainer === oxidizingAgentTarget) {
                                targetContainer.innerHTML = '<p>اسحب العامل المؤكسد هنا</p>';
                            } else {
                                targetContainer.innerHTML = '<p>اسحب العامل المختزل هنا</p>';
                            }

                            // Clear feedback
                            feedback.textContent = '';
                            feedback.className = 'redox-feedback';
                        });
                    }
                });
            }

            // Set up both target areas
            setupTarget(oxidizingAgentTarget);
            setupTarget(reducingAgentTarget);

            // Check button
            checkButton.addEventListener('click', function() {
                // Get the current answers
                const oxidizingPiece = oxidizingAgentTarget.querySelector('.redox-piece');
                const reducingPiece = reducingAgentTarget.querySelector('.redox-piece');

                if (!oxidizingPiece || !reducingPiece) {
                    feedback.textContent = 'يرجى تحديد المؤكسد والمرجع';
                    feedback.className = 'redox-feedback incorrect';
                    return;
                }

                const oxidizingAnswer = oxidizingPiece.dataset.value;
                const reducingAnswer = reducingPiece.dataset.value;

                // Check if both are correct
                if (oxidizingAnswer === correctOxidizingAgent && reducingAnswer === correctReducingAgent) {
                    feedback.textContent = 'إجابة صحيحة! أحسنت!';
                    feedback.className = 'redox-feedback correct';

                    // Disable dragging once correct
                    oxidizingAgentTarget.querySelectorAll('.redox-piece').forEach(piece => {
                        piece.draggable = false;
                    });
                    reducingAgentTarget.querySelectorAll('.redox-piece').forEach(piece => {
                        piece.draggable = false;
                    });
                } else if (oxidizingAnswer === correctOxidizingAgent) {
                    feedback.textContent = 'المؤكسد صحيح، لكن المرجع غير صحيح.';
                    feedback.className = 'redox-feedback incorrect';
                } else if (reducingAnswer === correctReducingAgent) {
                    feedback.textContent = 'المرجع صحيح، لكن المؤكسد غير صحيح.';
                    feedback.className = 'redox-feedback incorrect';
                } else {
                    feedback.textContent = 'كلا الإجابتين غير صحيحة. حاول مرة أخرى.';
                    feedback.className = 'redox-feedback incorrect';
                }
            });

            // Reset button
            resetButton.addEventListener('click', function() {
                // Clear the targets
                oxidizingAgentTarget.innerHTML = '<p>المؤكسد (Ox)</p>';
                reducingAgentTarget.innerHTML = '<p>المرجع (Red)</p>';

                // Unmark all pieces
                pieces.forEach(piece => {
                    piece.classList.remove('placed');
                });

                // Clear feedback
                feedback.textContent = '';
                feedback.className = 'redox-feedback';
            });
        }

        // Initialize redox exercises when the physics section is shown
        document.querySelector('button[onclick="showSection(\'physics-unit1-part1\')"]').addEventListener('click', function() {
            // Initialize after a short delay to ensure the DOM is ready
            setTimeout(() => {
                initHalfEquationExercise();
                initOxRedExercise();
            }, 100);
        });

        // Physics Unit 2 Quiz Functions
        function loadPhysicsUnit2Question() {
            // Get the current question
            const question = physicsUnit2Questions[currentPhysics2Question];

            // Set the question text
            document.getElementById("phys2-question").textContent = question.question;

            // Reset UI elements
            document.getElementById("phys2-answer-input").value = "";
            document.getElementById("phys2-result").textContent = "";
            document.getElementById("phys2-continue").style.display = "none";
            document.getElementById("phys2-retry").style.display = "none";
            document.getElementById("phys2-prev").style.display = "none";
            document.getElementById("phys2-submit").disabled = false;
            document.getElementById("phys2-skip").disabled = false;
            document.getElementById("phys2-hint").disabled = false;
            document.getElementById("phys2-learn").disabled = false;
            document.getElementById("phys2-answer-input").disabled = false;

            // Reset hint level for the new question
            physics2HintLevel = 0;

            // Make sure all relevant buttons are visible
            document.getElementById("phys2-skip").style.display = "inline-block";
            document.getElementById("phys2-submit").style.display = "inline-block";
            document.getElementById("phys2-hint").style.display = "inline-block";
            document.getElementById("phys2-learn").style.display = "inline-block";

            // Update progress indicator
            const progressText = `السؤال ${currentPhysics2Question + 1} من ${physicsUnit2Questions.length}`;
            document.getElementById("phys2-progress").textContent = progressText;
        }

        // Physics Unit 2 Quiz Event Listeners
        document.getElementById("phys2-submit").addEventListener("click", function() {
            const userAnswer = document.getElementById("phys2-answer-input").value.trim();
            const correctAnswer = physicsUnit2Questions[currentPhysics2Question].answer;

            // Calculate similarity between user answer and correct answer
            const similarity = calculateSimilarity(userAnswer, correctAnswer);
            let feedback = "";
            let scoreToAdd = 0;

            if (similarity >= 80) {
                scoreToAdd = 1;
                feedback = "✅ إجابة صحيحة";
            } else if (similarity >= 50) {
                scoreToAdd = 0.5;
                feedback = "⚠️ إجابة صحيحة جزئياً";
            } else {
                scoreToAdd = 0;
                feedback = "❌ إجابة غير صحيحة";
            }

            // Apply hint penalty if hints were used
            if (physics2HintLevel > 0 && scoreToAdd > 0) {
                const totalPenalty = hintPenalty * physics2HintLevel;
                scoreToAdd = Math.max(0, scoreToAdd - totalPenalty); // Ensure score doesn't go negative
                feedback += ` (تم خصم ${totalPenalty * 100}% لاستخدام التلميح)`;
            }

            // Add the score
            physics2Score += scoreToAdd;

            // Show feedback and comparison
            document.getElementById("phys2-result").innerHTML = `
                <div>${feedback}</div>
                ${generateComparisonHTML(userAnswer, correctAnswer)}
            `;

            // Disable input and buttons
            document.getElementById("phys2-submit").disabled = true;
            document.getElementById("phys2-skip").disabled = true;
            document.getElementById("phys2-hint").disabled = true;
            document.getElementById("phys2-learn").disabled = true;
            document.getElementById("phys2-answer-input").disabled = true;

            // Show continue and retry buttons
            document.getElementById("phys2-continue").style.display = "inline-block";
            document.getElementById("phys2-retry").style.display = "inline-block";

            // Show previous question button if we're not on the first question
            if (currentPhysics2Question > 0) {
                document.getElementById("phys2-prev").style.display = "inline-block";
            }
        });

        document.getElementById("phys2-skip").addEventListener("click", function() {
            currentPhysics2Question++;
            if (currentPhysics2Question < physicsUnit2Questions.length) {
                loadPhysicsUnit2Question();
            } else {
                // Show final score
                let resultTitle = `النتيجة النهائية: ${physics2Score} من ${physicsUnit2Questions.length}`;
                document.getElementById("phys2-question").textContent = resultTitle;
                document.getElementById("phys2-answers-block").style.display = "none";
                document.getElementById("phys2-submit").style.display = "none";
                document.getElementById("phys2-skip").style.display = "none";
                document.getElementById("phys2-prev").style.display = "none";
                document.getElementById("phys2-retry").style.display = "none";
                document.getElementById("phys2-hint").style.display = "none";
                document.getElementById("phys2-learn").style.display = "none";
                document.getElementById("phys2-continue").style.display = "none";
            }
        });

        document.getElementById("phys2-retry").addEventListener("click", function() {
            // Reload the current question
            loadPhysicsUnit2Question();
        });

        document.getElementById("phys2-prev").addEventListener("click", function() {
            if (currentPhysics2Question > 0) {
                currentPhysics2Question--;
                loadPhysicsUnit2Question();
            }
        });

        document.getElementById("phys2-continue").addEventListener("click", function() {
            currentPhysics2Question++;
            if (currentPhysics2Question < physicsUnit2Questions.length) {
                loadPhysicsUnit2Question();
            } else {
                // Show final score
                let resultTitle = `النتيجة النهائية: ${physics2Score} من ${physicsUnit2Questions.length}`;
                document.getElementById("phys2-question").textContent = resultTitle;
                document.getElementById("phys2-answers-block").style.display = "none";
                document.getElementById("phys2-submit").style.display = "none";
                document.getElementById("phys2-skip").style.display = "none";
                document.getElementById("phys2-prev").style.display = "none";
                document.getElementById("phys2-retry").style.display = "none";
                document.getElementById("phys2-hint").style.display = "none";
                document.getElementById("phys2-learn").style.display = "none";
                document.getElementById("phys2-continue").style.display = "none";
            }
        });

        document.getElementById("phys2-hint").addEventListener("click", function() {
            // Increment hint level for this question
            physics2HintLevel++;

            // Get the correct answer
            const correctAnswer = physicsUnit2Questions[currentPhysics2Question].answer;

            // Generate smart hint using our new function
            const hint = createSmartHint(correctAnswer, physics2HintLevel);

            // Calculate total penalty
            const totalPenalty = hintPenalty * physics2HintLevel;

            // Display the hint
            document.getElementById("phys2-result").innerHTML = `
                <div class="hint-container">
                    <strong>تلميح (${physics2HintLevel}):</strong> ${hint}
                    <p class="hint-penalty">سيتم خصم ${totalPenalty * 100}% من نقاطك لهذا السؤال</p>
                </div>
            `;

            // Disable the hint button if all letters are revealed
            const words = correctAnswer.split(' ');
            const totalLetters = words.reduce((sum, word) => sum + word.length, 0);
            if (physics2HintLevel >= totalLetters) {
                document.getElementById("phys2-hint").disabled = true;
            }
        });

        document.getElementById("phys2-learn").addEventListener("click", function() {
            const correctAnswer = physicsUnit2Questions[currentPhysics2Question].answer;

            // Display the answer with an explanation
            document.getElementById("phys2-result").innerHTML = `
                <div class="learn-container">
                    <strong>الإجابة:</strong> ${correctAnswer}
                    <p class="learn-note">ملاحظة: استخدام زر "عرض الإجابة/تعلم" لا يضيف نقاطاً.</p>
                </div>
            `;

            // Show the continue button to move to the next question
            document.getElementById("phys2-continue").style.display = "inline-block";

            // Disable the submit button since the answer is shown
            document.getElementById("phys2-submit").disabled = true;
            document.getElementById("phys2-hint").disabled = true;
            document.getElementById("phys2-learn").disabled = true;
        });

        document.getElementById("phys2-reset").addEventListener("click", function() {
            // Reset the quiz
            physicsUnit2Questions = shuffleArray(physicsUnit2QuestionsOriginal);
            currentPhysics2Question = 0;
            physics2Score = 0;

            // Reset UI elements
            document.getElementById("phys2-answers-block").style.display = "block";
            document.getElementById("phys2-submit").style.display = "inline-block";
            document.getElementById("phys2-skip").style.display = "inline-block";
            document.getElementById("phys2-hint").style.display = "inline-block";
            document.getElementById("phys2-learn").style.display = "inline-block";

            loadPhysicsUnit2Question();
        });

        // Function to check Islamic Law answers
        function checkIslamicLawAnswer() {
            const userAnswer = document.getElementById("islamic-answer-input").value.trim();
            if (!userAnswer) {
                document.getElementById("islamic-result").innerHTML = `
                    <div class="warning">
                        <p>يرجى إدخال إجابة قبل التحقق.</p>
                    </div>
                `;
                return;
            }

            const question = islamicLawQuestions[currentIslamicQuestion];
            const correctAnswers = question.answers;

            // Check if the user's answer matches any of the correct answers
            let bestMatch = { answer: "", similarity: 0 };
            let matchFound = false;

            for (const correctAnswer of correctAnswers) {
                const similarity = calculateSimilarity(userAnswer, correctAnswer);
                if (similarity > bestMatch.similarity) {
                    bestMatch = { answer: correctAnswer, similarity: similarity };
                }

                // If similarity is high enough, consider it a match
                if (similarity >= 0.8) {
                    matchFound = true;
                }
            }

            // Apply hint penalty if hints were used
            let scoreForQuestion = 1;
            if (islamicHintLevel > 0) {
                scoreForQuestion = Math.max(0, 1 - (islamicHintLevel * hintPenalty));
            }

            if (matchFound) {
                // Correct answer
                islamicScore += scoreForQuestion;

                // Show success message
                document.getElementById("islamic-result").innerHTML = `
                    <div class="correct-answer">
                        <h3>إجابة صحيحة!</h3>
                        <p>لقد أجبت بشكل صحيح على أحد أسباب انحراف العقيدة الإسلامية.</p>
                        <p>جميع الأسباب:</p>
                        <ul>${correctAnswers.map(answer => `<li>${answer}</li>`).join('')}</ul>
                    </div>
                `;

                // Disable input and buttons
                document.getElementById("islamic-answer-input").disabled = true;
                document.getElementById("islamic-submit").style.display = "none";
                document.getElementById("islamic-skip").disabled = true;
                document.getElementById("islamic-hint").disabled = true;

                // Show continue button in place of submit button
                document.getElementById("islamic-continue").style.display = "inline-block";
            } else {
                // Incorrect answer
                let resultHTML = `
                    <div class="incorrect-answer">
                        <h3>إجابة غير صحيحة</h3>
                        <p>إجابتك لا تتطابق مع أي من الأسباب المذكورة.</p>
                        <p>حاول مرة أخرى أو استخدم زر "تلميح" للمساعدة.</p>
                    </div>
                `;

                document.getElementById("islamic-result").innerHTML = resultHTML;
            }
        }

        // Initialize
        loadGeographyQuestion();
        loadHistoryQuestion();
        loadCrosswordQuestion();
        loadBiologyQuestion();
        loadIslamicLawQuestion();
    </script>
</body>
</html>
