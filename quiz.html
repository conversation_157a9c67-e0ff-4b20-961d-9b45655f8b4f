<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار تفاعلي</title>
    <link href="https://fonts.googleapis.com/css2?family=Cairo&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Cairo', Arial, sans-serif;
            background-color: #f0f0f0;
            text-align: right;
        }

        #quiz-container {
            width: 80%;
            margin: auto;
            padding: 20px;
            background-color: #fff;
            border: 1px solid #ddd;
            border-radius: 10px;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
        }

        #question-block {
            padding: 20px;
            border-bottom: 1px solid #ddd;
        }

        #question {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 10px;
        }

        #answers-block {
            padding: 20px;
        }

        #answers {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        #answers li {
            margin-bottom: 10px;
        }

        #answers li input[type="radio"] {
            margin-left: 10px;
            margin-right: 0;
        }

        #submit {
            background-color: #4CAF50;
            color: #fff;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
        }

        #reset {
            background-color: #f44336;
            color: #fff;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin-right: 10px;
        }

        .answer-input-container {
            margin: 20px 0;
        }

        #answer-input {
            width: 100%;
            padding: 10px;
            border: 2px solid #ddd;
            border-radius: 5px;
            font-family: 'Cairo', Arial, sans-serif;
            font-size: 16px;
            resize: vertical;
            direction: rtl;
        }

        #answer-input:focus {
            outline: none;
            border-color: #4CAF50;
        }

        #result {
            font-size: 20px;
            font-weight: bold;
            margin-top: 15px;
            text-align: center;
            line-height: 1.6;
        }

        #continue {
            background-color: #2196F3;
            color: #fff;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            display: none;
            margin-left: 10px;
        }

        #continue:hover {
            background-color: #1976D2;
        }

        .sentence-comparison {
            margin: 10px 0;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
    </style>
</head>
<body>
    <div id="quiz-container">
        <div id="question-block">
            <h2 id="question"></h2>
            <p id="question-text"></p>
        </div>
        <div id="answers-block">
            <ul id="answers"></ul>
        </div>
        <button id="submit">إرسال الإجابة</button>
        <button id="continue">التالي</button>
        <button id="reset">إعادة الاختبار</button>
        <p id="result"></p>
    </div>

    <script>
        let quizQuestions = [
            {
                question: "تبييض الأموال: إخفاء المصدر الحقيقي غير المشروع للأموال, ظهر هذا المصطلح في الو.م.أ سنة 1988 ويعني إعادة رسكلة الأموال غير المشروعة الناتجة عن تجارة (المخدرات-الأسلحة-الوثائق المزورة).",
                questionText: "",
                answers: [],
                correct: 0
            },
            {
                question: "المضاربة:هي المخاطرة بالبيع والشراء بناءاََ على توقع تقلبات أسعار السوق",
                questionText: "",
                answers: [],
                correct: 0
            },
            {
                question: "التنمية: حركة تطوير المجالات المختلفة, باستغلال رؤوس الأموال والتحهيزات والإمكانات البشرية وهي متعددة الجوانب: البشرية,الإقتصادية, الإجتماعية...",
                questionText: "",
                answers: [],
                correct: 0
            },
            {
                question: "المعيار: هو المقياس المعتمد من المنظمات والهيئات التابعة للأمم المتحدة لتصنييف الدول متقدمة أو متخلفة.",
                questionText: "",
                answers: [],
                correct: 0
            },
            {
                question: "المؤشر: هو الدليل الرقمي أو الإحصائيات والنسب  التي تثبت وتوضح واقع ظاهرة معينة كنسبة النمو الديمغرافي أو كمية الإنتاج أو متوسط العمر ونسبة المساهمة في التجارة الدولية (أرقام)",
                questionText: "",
                answers: [],
                correct: 0
            }
        ];

        let currentQuestion = 0;
        let score = 0;

        function loadQuestion() {
            let question = quizQuestions[currentQuestion];
            document.getElementById("question").textContent = question.question.split(":")[0];
            document.getElementById("question-text").style.display = "none";
            document.getElementById("result").textContent = "";
            document.getElementById("continue").style.display = "none";
            
            let answersList = document.getElementById("answers");
            answersList.innerHTML = `
                <div class="answer-input-container">
                    <textarea id="answer-input" rows="3" placeholder="اكتب إجابتك هنا... (ملاحظة: ترتيب الجمل مهم في اللغة العربية للحصول على معنى واضح)"></textarea>
                </div>
            `;
        }

        function normalizeArabicText(text) {
            return text
                // Remove diacritics (tashkeel)
                .replace(/[\u064B-\u065F]/g, '')
                // Normalize different forms of Hamza
                .replace(/[إأآا]/g, 'ا')
                // Remove all punctuation including parentheses and dashes
                .replace(/[.,():\-،]/g, ' ')
                // Normalize الو.م.أ variations
                .replace(/الو\.م\.أ|الوما|الولايات المتحدة الأمريكية|الولايات المتحدة الامريكية/g, 'الولايات المتحدة الامريكية')
                // Handle common word variations
                .replace(/رسكلة|رسلكة/g, 'رسكلة')
                // Remove extra spaces
                .trim()
                .replace(/\s+/g, ' ')
                // Convert to lowercase (for consistency)
                .toLowerCase();
        }

        function calculateSimilarity(userAnswer, correctAnswer) {
            const userWords = userAnswer.split(' ');
            const correctWords = correctAnswer.split(' ');
            let matchedWords = 0;
            let lastMatchIndex = -1;
            
            correctWords.forEach(correctWord => {
                const normalizedCorrectWord = normalizeArabicText(correctWord);
                
                // Search for matching word only after the last match
                for (let i = lastMatchIndex + 1; i < userWords.length; i++) {
                    if (normalizeArabicText(userWords[i]) === normalizedCorrectWord) {
                        matchedWords++;
                        lastMatchIndex = i;
                        break;
                    }
                }
            });
            
            return (matchedWords / correctWords.length) * 100;
        }

        function compareAnswers(userAnswer, correctAnswer) {
            const userWords = userAnswer.split(' ');
            const correctWords = correctAnswer.split(' ');
            let comparisonHTML = '';
            let lastMatchIndex = -1;
            
            correctWords.forEach((correctWord, correctIndex) => {
                const normalizedCorrectWord = normalizeArabicText(correctWord);
                
                // Search for matching word only after the last match
                let found = false;
                for (let i = lastMatchIndex + 1; i < userWords.length; i++) {
                    if (normalizeArabicText(userWords[i]) === normalizedCorrectWord) {
                        found = true;
                        lastMatchIndex = i;
                        break;
                    }
                }
                
                if (found) {
                    comparisonHTML += `<span style="color: #4CAF50">${correctWord}</span> `;
                } else {
                    comparisonHTML += `<span style="color: #ff4444">${correctWord}</span> `;
                }
            });
            
            return `<div class="answer-comparison">${comparisonHTML}</div>`;
        }

        document.getElementById("submit").addEventListener("click", function() {
            let userAnswer = document.getElementById("answer-input").value.trim();
            let correctAnswer = quizQuestions[currentQuestion].question.split(":")[1].trim();
            let resultElement = document.getElementById("result");
            let continueButton = document.getElementById("continue");

            if (!userAnswer) {
                alert("الرجاء كتابة إجابتك");
                return;
            }

            const similarity = calculateSimilarity(userAnswer, correctAnswer);
            let feedback = "";
            
            if (similarity >= 80) {
                score++;
                feedback = "✅ إجابة صحيحة (100%)";
                
                if (!hasProperFormatting(userAnswer)) {
                    feedback += "<br><br>ملاحظة مهمة: إجابتك صحيحة من حيث المحتوى، لكن في امتحان البكالوريا، يجب الانتباه إلى:";
                    feedback += "<br>• استخدام علامات الترقيم المناسبة (،:.-) ";
                    feedback += "<br>• تنظيم الجمل بشكل صحيح";
                    feedback += "<br>• الكتابة بأسلوب واضح ومنظم";
                }
                
                feedback += "<br><br>الترتيب الصحيح للإجابة:<br>";
                feedback += compareAnswers(userAnswer, correctAnswer);
                
                resultElement.innerHTML = feedback;
                resultElement.style.color = "#000";
            } else if (similarity >= 50) {
                score += 0.5;
                feedback = "⚠️ إجابة صحيحة جزئ<|im_start|>";
                feedback += "<br><br>إجابتك تحتوي على معظم العناصر الصحيحة، لكن هناك نقص في بعض التفاصيل أو الترتيب:";
                feedback += "<br><br>المقارنة مع الإجابة الصحيحة:<br>";
                feedback += compareAnswers(userAnswer, correctAnswer);
                
                resultElement.innerHTML = feedback;
                resultElement.style.color = "#ff8c00"; // Orange
            } else {
                feedback = "❌ إجابة غير صحيحة";
                feedback += "<br><br>المقارنة مع الإجابة الصحيحة:<br>";
                feedback += compareAnswers(userAnswer, correctAnswer);
                
                resultElement.innerHTML = feedback;
                resultElement.style.color = "#ff4444"; // Red
            }

            document.getElementById("submit").disabled = true;
            document.getElementById("answer-input").disabled = true;
            continueButton.style.display = "inline-block";
        });

        document.getElementById("continue").addEventListener("click", function() {
            currentQuestion++;
            if (currentQuestion < quizQuestions.length) {
                loadQuestion();
                document.getElementById("submit").disabled = false;
                document.getElementById("answer-input").disabled = false;
                document.getElementById("continue").style.display = "none";
                document.getElementById("result").textContent = "";
            } else {
                document.getElementById("question-block").style.display = "block";
                document.getElementById("answers-block").style.display = "none";
                document.getElementById("submit").style.display = "none";
                document.getElementById("continue").style.display = "none";
                document.getElementById("result").innerHTML = `النتيجة النهائية: ${score.toFixed(2)} من ${quizQuestions.length}`;
                document.getElementById("result").style.color = "#000";
            }
        });

        document.getElementById("reset").addEventListener("click", function() {
            currentQuestion = 0;
            score = 0;
            document.getElementById("question-block").style.display = "block";
            document.getElementById("answers-block").style.display = "block";
            document.getElementById("submit").style.display = "inline-block";
            document.getElementById("submit").disabled = false;
            document.getElementById("answer-input").disabled = false;
            document.getElementById("continue").style.display = "none";
            document.getElementById("result").textContent = "";
            loadQuestion();
        });

        // Initialize the first question
        loadQuestion();

        function hasProperFormatting(text) {
            // Check for proper formatting indicators
            const hasPunctuation = /[،.:،\-()]/.test(text);
            const hasProperStructure = text.includes("،") || text.includes(":") || text.includes("(");
            return hasPunctuation && hasProperStructure;
        }
    </script>
</body>
</html>






















