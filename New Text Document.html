<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Interactive Quiz</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background-color: #f0f0f0;
        }

        #quiz-container {
            width: 80%;
            margin: auto;
            padding: 20px;
            background-color: #fff;
            border: 1px solid #ddd;
            border-radius: 10px;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
        }

        #question-block {
            padding: 20px;
            border-bottom: 1px solid #ddd;
        }

        #question {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 10px;
        }

        #answers-block {
            padding: 20px;
        }

        #answers {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        #answers li {
            margin-bottom: 10px;
        }

        #answers li input[type="radio"] {
            margin-right: 10px;
        }

        #answers li label {
            font-size: 18px;
        }

        #submit {
            background-color: #4CAF50;
            color: #fff;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
        }

        #submit:hover {
            background-color: #3e8e41;
        }

        #reset {
            background-color: #f44336;
            color: #fff;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
        }

        #reset:hover {
            background-color: #e91e63;
        }

        #result {
            font-size: 24px;
            font-weight: bold;
            margin-top: 20px;
        }

        #scroll-indicator {
            position: fixed;
            bottom: 20px;
            right: 20px;
            font-size: 18px;
            color: #666;
        }
    </style>
</head>
<body>
    <div id="quiz-container">
        <div id="question-block">
            <h2 id="question"></h2>
            <p id="question-text"></p>
        </div>
        <div id="answers-block">
            <ul id="answers"></ul>
        </div>
        <button id="submit">Submit</button>
        <button id="reset">Reset Quiz</button>
        <p id="result"></p>
    </div>
    <div id="scroll-indicator"></div>

    <script>
        let quizQuestions = [
            {
                question: "What is the capital of France?",
                questionText: "France is a country located in Western Europe. It is known for its rich history, art, fashion, and cuisine. What is the capital city of France?",
                answers: ["Berlin", "Paris", "London", "Rome"],
                correct: 1
            },
            {
                question: "What is the largest planet in our solar system?",
                questionText: "Our solar system consists of eight planets. Which one is the largest in terms of diameter?",
                answers: ["Earth", "Mars", "Jupiter", "Saturn"],
                correct: 2
            },
            {
                question: "Who painted the Mona Lisa?",
                questionText: "The Mona Lisa is one of the most famous paintings in the world. It was created by a renowned artist during the Italian Renaissance. Who was the artist?",
                answers: ["Leonardo da Vinci", "Michelangelo", "Raphael", "Caravaggio"],
                correct: 0
            }
        ];

        let currentQuestion = 0;
        let score = 0;
        let shuffledQuestions = shuffle(quizQuestions.slice());

        function shuffle(array) {
            return array.sort(() => Math.random() - 0.5);
        }

        function loadQuestion() {
            if (currentQuestion >= shuffledQuestions.length) {
                // Reset and shuffle questions for a new round
                currentQuestion = 0;
                shuffledQuestions = shuffle(quizQuestions.slice());
                score = 0;
            }
            
            let question = shuffledQuestions[currentQuestion];
            document.getElementById("question").textContent = question.question;
            document.getElementById("question-text").textContent = question.questionText;
            let answersList = document.getElementById("
        